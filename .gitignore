# Virtual Environment
.venv/
venv/
env/

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Distribution / packaging
dist/
build/
*.egg-info/

# Ruff cache
.ruff_cache/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover
.pytest_cache/

# Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Environment variables
.env
.env.staging
.env.prod

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Jupyter Notebook
.ipynb_checkpoints

# Celery
celerybeat-schedule
celerybeat.pid
