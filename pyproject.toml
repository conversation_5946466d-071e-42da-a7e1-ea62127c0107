[project]
name = "admin2"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.12"
readme = "README.md"
dependencies = [
    "celery>=5.5.2",
    "clickhouse-driver>=0.2.9",
    "django>=5.2",
    "django-allauth>=65.7.0",
    "django-celery-beat>=2.8.1",
    "django-celery-results>=2.6.0",
    "django-cleanup>=9.0.0",
    "django-environ>=0.12.0",
    "django-filter>=25.1",
    "django-htmx>=1.23.0",
    "django-template-partials>=24.4",
    "django-widget-tweaks>=1.5.0",
    "djangorestframework>=3.16.0",
    "eventlet>=0.40.0",
    "gunicorn>=23.0.0",
    "markdown>=3.8.2",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "pillow>=11.2.1",
    "psycopg[binary,pool]>=3.2.9",
    "redis>=6.1.0",
]

[tool.uv]
package = false

[dependency-groups]
dev = [
    "django-extensions>=4.1",
    "factory-boy>=3.3.3",
    "faker>=37.1.0",
    "pre-commit>=4.2.0",
    "pytest>=8.3.5",
    "pytest-django>=4.11.1",
    "pytest-xdist>=3.6.1",
    "ruff>=0.11.8",
    "werkzeug>=3.1.3",
]
