import datetime

import factory
from django.utils import timezone
from factory.django import DjangoModelFactory

from accounts.models import User
from common.models import Supplier
from pricelens.models import (
    BucketChoices,
    CadenceProfile,
    FailReason,
    Investigation,
    InvestigationStatus,
)


class FailReasonFactory(DjangoModelFactory):
    class Meta:
        model = FailReason

    code = factory.Sequence(lambda n: f"ERROR_CODE_{n}")
    name = factory.Faker("sentence")
    description = factory.Faker("text")


class SupplierFactory(DjangoModelFactory):
    class Meta:
        model = Supplier

    supid = factory.Sequence(lambda n: n)
    name = factory.Faker("company")


class UserFactory(DjangoModelFactory):
    class Meta:
        model = User
        django_get_or_create = ("username",)

    username = factory.Faker("user_name")
    email = factory.Faker("email")


class InvestigationFactory(DjangoModelFactory):
    class Meta:
        model = Investigation

    supplier = factory.SubFactory(SupplierFactory)
    fail_reason = factory.SubFactory(FailReasonFactory)
    event_dt = factory.Faker("date_time", tzinfo=factory.LazyFunction(lambda: timezone.get_current_timezone()))
    stage = factory.Faker("random_element", elements=["load_mail", "consolidate"])
    status = InvestigationStatus.OPEN


class CadenceProfileFactory(DjangoModelFactory):
    class Meta:
        model = CadenceProfile

    supplier = factory.SubFactory(SupplierFactory)
    median_gap_days = factory.Faker("pyint", min_value=1, max_value=30)
    sd_gap = factory.Faker("pyfloat", left_digits=2, right_digits=2, positive=True)
    days_since_last = factory.Faker("pyint", min_value=0, max_value=100)
    last_success_date = factory.LazyAttribute(
        lambda o: timezone.now().date() - datetime.timedelta(days=o.days_since_last)
    )
    bucket = factory.Faker("random_element", elements=BucketChoices.values)


class SupplierDataFactory(factory.Factory):
    """Factory for creating supplier data dictionaries."""

    class Meta:
        model = dict

    price = factory.Faker("pydecimal", left_digits=4, right_digits=2, positive=True)
    quantity = factory.Faker("pyint", min_value=1, max_value=100)
    supplier_name = factory.Faker("company")


class InputDataFactory(factory.Factory):
    """Factory for creating input data dictionaries."""

    class Meta:
        model = dict

    Бренд = factory.Faker("random_element", elements=["HYUNDAI/KIA/MOBIS", "VAG", "NISSAN", "SSANGYONG"])
    Артикул = factory.Faker("bothify", text="#######??")
