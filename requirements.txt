# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --output-file requirements.txt
amqp==5.3.1
    # via kombu
asgiref==3.8.1
    # via
    #   django
    #   django-allauth
    #   django-htmx
billiard==4.2.1
    # via celery
celery==5.5.3
    # via
    #   admin2 (pyproject.toml)
    #   django-celery-beat
    #   django-celery-results
click==8.2.1
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
clickhouse-driver==0.2.9
    # via admin2 (pyproject.toml)
colorama==0.4.6
    # via click
cron-descriptor==1.4.5
    # via django-celery-beat
django==5.2.3
    # via
    #   admin2 (pyproject.toml)
    #   django-allauth
    #   django-celery-beat
    #   django-celery-results
    #   django-filter
    #   django-htmx
    #   django-template-partials
    #   django-timezone-field
    #   djangorestframework
django-allauth==65.9.0
    # via admin2 (pyproject.toml)
django-celery-beat==2.8.1
    # via admin2 (pyproject.toml)
django-celery-results==2.6.0
    # via admin2 (pyproject.toml)
django-cleanup==9.0.0
    # via admin2 (pyproject.toml)
django-environ==0.12.0
    # via admin2 (pyproject.toml)
django-filter==25.1
    # via admin2 (pyproject.toml)
django-htmx==1.23.0
    # via admin2 (pyproject.toml)
django-template-partials==24.4
    # via admin2 (pyproject.toml)
django-timezone-field==7.1
    # via django-celery-beat
django-widget-tweaks==1.5.0
    # via admin2 (pyproject.toml)
djangorestframework==3.16.0
    # via admin2 (pyproject.toml)
dnspython==2.7.0
    # via eventlet
et-xmlfile==2.0.0
    # via openpyxl
eventlet==0.40.0
    # via admin2 (pyproject.toml)
greenlet==3.2.3
    # via eventlet
gunicorn==23.0.0
    # via admin2 (pyproject.toml)
kombu==5.5.4
    # via celery
markdown==3.8.2
    # via admin2 (pyproject.toml)
numpy==2.3.0
    # via pandas
openpyxl==3.1.5
    # via admin2 (pyproject.toml)
packaging==25.0
    # via
    #   gunicorn
    #   kombu
pandas==2.3.0
    # via admin2 (pyproject.toml)
pillow==11.2.1
    # via admin2 (pyproject.toml)
prompt-toolkit==3.0.51
    # via click-repl
psycopg==3.2.9
    # via admin2 (pyproject.toml)
psycopg-binary==3.2.9
    # via psycopg
psycopg-pool==3.2.6
    # via psycopg
python-crontab==3.3.0
    # via django-celery-beat
python-dateutil==2.9.0.post0
    # via
    #   celery
    #   pandas
pytz==2025.2
    # via
    #   clickhouse-driver
    #   pandas
redis==6.2.0
    # via admin2 (pyproject.toml)
six==1.17.0
    # via python-dateutil
sqlparse==0.5.3
    # via django
typing-extensions==4.14.0
    # via
    #   psycopg
    #   psycopg-pool
tzdata==2025.2
    # via
    #   django
    #   django-celery-beat
    #   kombu
    #   pandas
    #   psycopg
    #   tzlocal
tzlocal==5.3.1
    # via clickhouse-driver
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
