# 4. TDD Implementation Plan

## Test-Driven Development Approach for Cross-Dock Excel Generation

This document outlines our Test-Driven Development (TDD) approach for implementing the cross-dock Excel generation functionality. We'll start by writing tests that define the expected behavior, then implement the code to make those tests pass.

## Testing Libraries and Tools

We'll use the following libraries for our testing approach:

1. **pytest** - Our main testing framework
2. **pytest-django** - For Django-specific testing utilities
3. **factory-boy** - For creating test objects/fixtures in a maintainable way
4. **faker** - For generating realistic test data

## Best Practices for Testing

1. **Follow the AAA Pattern**
   - **Arrange**: Set up test data and preconditions
   - **Act**: Execute the code being tested
   - **Assert**: Verify the results

2. **Use Descriptive Test Names**
   - Test names should clearly describe what's being tested
   - Example: `test_process_cross_dock_data_handles_empty_results`

3. **One Assertion Per Test**
   - Each test should verify a single behavior
   - Makes tests easier to understand and maintain

4. **Use Fixtures for Common Setup**
   - Create reusable test fixtures with pytest's fixture system
   - Reduces code duplication and improves maintainability

5. **Mock External Dependencies**
   - Isolate tests from external systems like ClickHouse
   - Ensures tests are fast, reliable, and focused

6. **Test Edge Cases**
   - Empty data sets
   - Error conditions
   - Boundary values

7. **Use Parameterized Tests**
   - Test multiple scenarios with different inputs
   - Reduces code duplication

8. **Keep Tests Independent**
   - Tests should not depend on each other
   - Each test should be able to run in isolation

## Test Structure

We'll create a test folder structure that mirrors our application structure:

```
tests/
├── common/
│   └── utils/
│       └── test_excel.py
└── cross_dock/
    └── services/
        ├── test_clickhouse_service.py
        └── test_excel_service.py
```

## Implementation Order

1. **Create Test Structure**
   - Create tests directory with subdirectories mirroring the application structure
   - Write tests for Excel utilities, ClickHouse service, and Excel service

2. **Run Tests (They Should Fail)**
   - Verify that all tests fail as expected since we haven't implemented the code yet

3. **Implement Excel Utilities**
   - Create common/utils/excel.py with create_workbook and save_workbook functions
   - Run tests to verify they pass

4. **Enhance ClickHouse Service**
   - Update cross_dock/services/clickhouse_service.py if needed
   - Run tests to verify they pass

5. **Implement Excel Service**
   - Create cross_dock/services/excel_service.py with process_cross_dock_data function
   - Run tests to verify they pass

6. **Implement File Processing**
   - Add process_cross_dock_data_from_file function to excel_service.py
   - Run integration tests to verify they pass

7. **Implement Views and URLs**
   - Update cross_dock/views.py with necessary views
   - Update cross_dock/urls.py to add routes
   - Create templates if needed

8. **Manual Testing**
   - Test the full workflow manually with real data

## Detailed Test Plan

### 1. Excel Utilities Tests (tests/common/utils/test_excel.py)

```python
import os
import tempfile
from unittest import TestCase, mock
from django.conf import settings

# Import will fail until we implement the code
# from common.utils.excel import create_workbook, save_workbook

class TestExcelUtilities(TestCase):
    def test_create_workbook(self):
        """Test that create_workbook returns a valid workbook object."""
        # This will fail until we implement create_workbook
        wb = create_workbook()
        self.assertIsNotNone(wb)
        self.assertTrue(hasattr(wb, 'active'))
    
    def test_save_workbook(self):
        """Test that save_workbook correctly saves a workbook to a file."""
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock settings to use the temp directory
            with mock.patch('django.conf.settings.MEDIA_ROOT', temp_dir):
                with mock.patch('django.conf.settings.MEDIA_URL', '/media/'):
                    # Create a workbook
                    wb = create_workbook()
                    
                    # Save the workbook
                    filename = "test_workbook.xlsx"
                    url = save_workbook(wb, filename)
                    
                    # Check that the file exists
                    file_path = os.path.join(temp_dir, 'exports', filename)
                    self.assertTrue(os.path.exists(file_path))
                    
                    # Check that the URL is correct
                    expected_url = '/media/exports/test_workbook.xlsx'
                    self.assertEqual(url, expected_url)
```

### 2. ClickHouse Service Tests (tests/cross_dock/services/test_clickhouse_service.py)

```python
from unittest import TestCase, mock
import pandas as pd

# Import will fail until we implement the code
# from cross_dock.services.clickhouse_service import query_supplier_data

class TestClickHouseService(TestCase):
    @mock.patch('cross_dock.services.clickhouse_service.get_clickhouse_client')
    def test_query_supplier_data(self, mock_get_client):
        """Test that query_supplier_data correctly queries ClickHouse."""
        # Mock the ClickHouse client
        mock_client = mock.MagicMock()
        mock_get_client.return_value.__enter__.return_value = mock_client
        
        # Mock the execute method to return test data
        mock_client.execute.side_effect = [
            # First call - suppliers query
            [(1001,), (1002,), (1003,)],
            # Second call - price query
            [
                (100.50, 5, "Supplier A"),
                (120.75, 10, "Supplier B"),
                (90.25, 3, "Supplier C")
            ]
        ]
        
        # Call the function
        result = query_supplier_data("HYUNDAI/KIA/MOBIS", "223112e100", "emex")
        
        # Check that the result is a DataFrame with the expected columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(list(result.columns), ["price", "quantity", "supplier_name"])
        
        # Check that the data is correct
        self.assertEqual(len(result), 3)
        self.assertEqual(result.iloc[0]["price"], 100.50)
        self.assertEqual(result.iloc[0]["quantity"], 5)
        self.assertEqual(result.iloc[0]["supplier_name"], "Supplier A")
    
    @mock.patch('cross_dock.services.clickhouse_service.get_clickhouse_client')
    def test_query_supplier_data_no_suppliers(self, mock_get_client):
        """Test handling of no suppliers found."""
        # Mock the ClickHouse client
        mock_client = mock.MagicMock()
        mock_get_client.return_value.__enter__.return_value = mock_client
        
        # Mock the execute method to return empty results
        mock_client.execute.return_value = []
        
        # Call the function
        result = query_supplier_data("BRAND", "ARTICLE", "emex")
        
        # Check that the result is an empty DataFrame with the expected columns
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(list(result.columns), ["price", "quantity", "supplier_name"])
        self.assertEqual(len(result), 0)
    
    @mock.patch('cross_dock.services.clickhouse_service.get_clickhouse_client')
    def test_query_supplier_data_exception(self, mock_get_client):
        """Test error handling in query_supplier_data."""
        # Mock the ClickHouse client to raise an exception
        mock_client = mock.MagicMock()
        mock_get_client.return_value.__enter__.return_value = mock_client
        mock_client.execute.side_effect = Exception("Test exception")
        
        # Check that the function raises the exception
        with self.assertRaises(Exception):
            query_supplier_data("BRAND", "ARTICLE", "emex")
```

### 3. Excel Service Tests (tests/cross_dock/services/test_excel_service.py)

```python
import os
import tempfile
from unittest import TestCase, mock
import pandas as pd
from openpyxl import load_workbook

# Import will fail until we implement the code
# from cross_dock.services.excel_service import process_cross_dock_data

class TestExcelService(TestCase):
    @mock.patch('cross_dock.services.excel_service.query_supplier_data')
    def test_process_cross_dock_data(self, mock_query_supplier_data):
        """Test processing cross-dock data and generating an Excel file."""
        # Mock the query_supplier_data function
        mock_query_supplier_data.return_value = pd.DataFrame({
            "price": [100.50, 120.75, 90.25],
            "quantity": [5, 10, 3],
            "supplier_name": ["Supplier A", "Supplier B", "Supplier C"]
        })
        
        # Test data
        test_data = [
            {"Бренд": "HYUNDAI/KIA/MOBIS", "Артикул": "223112e100"},
            {"Бренд": "VAG", "Артикул": "000915105cd"}
        ]
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock settings to use the temp directory
            with mock.patch('django.conf.settings.MEDIA_ROOT', temp_dir):
                with mock.patch('django.conf.settings.MEDIA_URL', '/media/'):
                    # Process the data
                    progress, file_url = process_cross_dock_data(test_data, "emex")
                    
                    # Check the progress
                    self.assertEqual(progress, "100%")
                    
                    # Check that the file exists
                    file_path = os.path.join(temp_dir, 'exports', os.path.basename(file_url))
                    self.assertTrue(os.path.exists(file_path))
                    
                    # Load the workbook and check its contents
                    wb = load_workbook(file_path)
                    sheet = wb.active
                    
                    # Check headers
                    headers = ["SKU", "Бренд", "Артикул", "Лучшая цена 1", "Количество 1", "Название поставщика 1",
                               "Лучшая цена 2", "Количество 2", "Название поставщика 2",
                               "Лучшая цена 3", "Количество 3", "Название поставщика 3"]
                    for col_num, header in enumerate(headers, start=1):
                        self.assertEqual(sheet.cell(row=1, column=col_num).value, header)
                    
                    # Check first row data
                    self.assertEqual(sheet.cell(row=2, column=1).value, "HYUNDAI/KIA/MOBIS|223112e100")  # SKU
                    self.assertEqual(sheet.cell(row=2, column=2).value, "HYUNDAI/KIA/MOBIS")  # Brand
                    self.assertEqual(sheet.cell(row=2, column=3).value, "223112e100")  # Article
                    self.assertEqual(sheet.cell(row=2, column=4).value, 100.50)  # Price 1
                    self.assertEqual(sheet.cell(row=2, column=5).value, 5)  # Quantity 1
                    self.assertEqual(sheet.cell(row=2, column=6).value, "Supplier A")  # Supplier 1
    
    @mock.patch('cross_dock.services.excel_service.query_supplier_data')
    def test_process_cross_dock_data_empty_results(self, mock_query_supplier_data):
        """Test handling of empty query results."""
        # Mock the query_supplier_data function to return empty results
        mock_query_supplier_data.return_value = pd.DataFrame(columns=["price", "quantity", "supplier_name"])
        
        # Test data
        test_data = [
            {"Бренд": "BRAND", "Артикул": "ARTICLE"}
        ]
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock settings to use the temp directory
            with mock.patch('django.conf.settings.MEDIA_ROOT', temp_dir):
                with mock.patch('django.conf.settings.MEDIA_URL', '/media/'):
                    # Process the data
                    progress, file_url = process_cross_dock_data(test_data, "emex")
                    
                    # Check the progress
                    self.assertEqual(progress, "100%")
                    
                    # Check that the file exists
                    file_path = os.path.join(temp_dir, 'exports', os.path.basename(file_url))
                    self.assertTrue(os.path.exists(file_path))
                    
                    # Load the workbook and check its contents
                    wb = load_workbook(file_path)
                    sheet = wb.active
                    
                    # Check that empty cells are properly handled
                    self.assertEqual(sheet.cell(row=2, column=4).value, None)  # Price 1
                    self.assertEqual(sheet.cell(row=2, column=5).value, None)  # Quantity 1
                    self.assertEqual(sheet.cell(row=2, column=6).value, None)  # Supplier 1
    
    @mock.patch('cross_dock.services.excel_service.query_supplier_data')
    def test_process_cross_dock_data_exception(self, mock_query_supplier_data):
        """Test error handling during data processing."""
        # Mock the query_supplier_data function to raise an exception
        mock_query_supplier_data.side_effect = Exception("Test exception")
        
        # Test data
        test_data = [
            {"Бренд": "BRAND", "Артикул": "ARTICLE"}
        ]
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock settings to use the temp directory
            with mock.patch('django.conf.settings.MEDIA_ROOT', temp_dir):
                with mock.patch('django.conf.settings.MEDIA_URL', '/media/'):
                    # Process the data
                    progress, file_url = process_cross_dock_data(test_data, "emex")
                    
                    # Check the progress
                    self.assertEqual(progress, "100%")
                    
                    # Check that the file exists
                    file_path = os.path.join(temp_dir, 'exports', os.path.basename(file_url))
                    self.assertTrue(os.path.exists(file_path))
                    
                    # Load the workbook and check its contents
                    wb = load_workbook(file_path)
                    sheet = wb.active
                    
                    # Check that error cells are properly handled
                    self.assertEqual(sheet.cell(row=2, column=4).value, None)  # Price 1
                    self.assertEqual(sheet.cell(row=2, column=5).value, None)  # Quantity 1
                    self.assertEqual(sheet.cell(row=2, column=6).value, None)  # Supplier 1
```

### 4. Integration Test (tests/cross_dock/test_integration.py)

```python
import os
import tempfile
from unittest import TestCase, mock
import pandas as pd
from openpyxl import Workbook, load_workbook

# Import will fail until we implement the code
# from cross_dock.services.excel_service import process_cross_dock_data_from_file

class TestIntegration(TestCase):
    @mock.patch('cross_dock.services.clickhouse_service.query_supplier_data')
    def test_process_cross_dock_data_from_file(self, mock_query_supplier_data):
        """Test processing cross-dock data from a file."""
        # Mock the query_supplier_data function
        mock_query_supplier_data.return_value = pd.DataFrame({
            "price": [100.50, 120.75, 90.25],
            "quantity": [5, 10, 3],
            "supplier_name": ["Supplier A", "Supplier B", "Supplier C"]
        })
        
        # Create a temporary input file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
        
        # Create a test input Excel file
        wb = Workbook()
        sheet = wb.active
        
        # Add headers
        sheet.cell(row=1, column=1, value="Бренд")
        sheet.cell(row=1, column=2, value="Артикул")
        
        # Add data
        sheet.cell(row=2, column=1, value="HYUNDAI/KIA/MOBIS")
        sheet.cell(row=2, column=2, value="223112e100")
        sheet.cell(row=3, column=1, value="VAG")
        sheet.cell(row=3, column=2, value="000915105cd")
        
        # Save the workbook
        wb.save(temp_file_path)
        
        try:
            # Process the file
            output_file_path = process_cross_dock_data_from_file(temp_file_path, "emex")
            
            # Check that the output file exists
            self.assertTrue(os.path.exists(output_file_path))
            
            # Load the output workbook and check its contents
            wb = load_workbook(output_file_path)
            sheet = wb.active
            
            # Check headers
            headers = ["SKU", "Бренд", "Артикул", "Лучшая цена 1", "Количество 1", "Название поставщика 1",
                       "Лучшая цена 2", "Количество 2", "Название поставщика 2",
                       "Лучшая цена 3", "Количество 3", "Название поставщика 3"]
            for col_num, header in enumerate(headers, start=1):
                self.assertEqual(sheet.cell(row=1, column=col_num).value, header)
            
            # Check first row data
            self.assertEqual(sheet.cell(row=2, column=1).value, "HYUNDAI/KIA/MOBIS|223112e100")  # SKU
            self.assertEqual(sheet.cell(row=2, column=2).value, "HYUNDAI/KIA/MOBIS")  # Brand
            self.assertEqual(sheet.cell(row=2, column=3).value, "223112e100")  # Article
            self.assertEqual(sheet.cell(row=2, column=4).value, 100.50)  # Price 1
            self.assertEqual(sheet.cell(row=2, column=5).value, 5)  # Quantity 1
            self.assertEqual(sheet.cell(row=2, column=6).value, "Supplier A")  # Supplier 1
            
            # Clean up
            os.unlink(output_file_path)
        finally:
            # Clean up the input file
            os.unlink(temp_file_path)
```

## Implementation Details

### 1. Excel Utilities (common/utils/excel.py)

```python
from openpyxl import Workbook
import os
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

def create_workbook():
    """Create a new Excel workbook."""
    wb = Workbook()
    return wb

def save_workbook(wb, filename):
    """
    Save workbook to the exports directory.
    
    Args:
        wb: The workbook to save
        filename: The filename to save as
        
    Returns:
        str: The URL to the saved file
    """
    # Create directory if it doesn't exist
    export_dir = os.path.join(settings.MEDIA_ROOT, 'exports')
    os.makedirs(export_dir, exist_ok=True)
    
    # Save the file
    file_path = os.path.join(export_dir, filename)
    wb.save(file_path)
    
    # Return the URL
    url = os.path.join(settings.MEDIA_URL, 'exports', filename)
    logger.info(f"Saved workbook to {file_path}, URL: {url}")
    return url
```

### 2. Excel Service (cross_dock/services/excel_service.py)

```python
import pandas as pd
import logging
from typing import Tuple, List, Dict, Any
import uuid
import os

from .clickhouse_service import query_supplier_data
from common.utils.excel import create_workbook, save_workbook

logger = logging.getLogger(__name__)

def process_cross_dock_data(data: List[Dict[str, str]], platform: str = "emex") -> Tuple[str, str]:
    """
    Process cross-dock data and generate Excel file.
    
    Args:
        data: List of dictionaries containing brand and article information
        platform: Platform to query (default: "emex")
        
    Returns:
        tuple: (progress percentage, file URL)
    """
    logger.info(f"Processing cross-dock data for platform {platform}")
    
    # Create workbook
    wb = create_workbook()
    result_sheet = wb.active
    
    # Add headers
    headers = ["SKU", "Бренд", "Артикул", "Лучшая цена 1", "Количество 1", "Название поставщика 1",
               "Лучшая цена 2", "Количество 2", "Название поставщика 2",
               "Лучшая цена 3", "Количество 3", "Название поставщика 3"]
    
    for col_num, header in enumerate(headers, start=1):
        result_sheet.cell(row=1, column=col_num, value=header)
    
    # Process each row
    total_rows = len(data)
    processed_rows = 0
    
    for row_num, item in enumerate(data, start=2):
        brand = item['Бренд']
        article = item['Артикул']
        logger.debug(f"Processing row {row_num-1}/{total_rows}: {brand}, {article}")
        
        # Create SKU
        sku = f"{brand}|{article}"
        
        # Write basic data
        result_sheet.cell(row=row_num, column=1, value=sku)
        result_sheet.cell(row=row_num, column=2, value=brand)
        result_sheet.cell(row=row_num, column=3, value=article)
        
        # Query ClickHouse for price data
        try:
            price_results = query_supplier_data(brand, article, platform)
            
            # Write price data
            for i, (_, price_row) in enumerate(price_results.iterrows(), start=0):
                if i < 3:  # Only use the first 3 results
                    price = price_row['price']
                    quantity = price_row['quantity']
                    supplier_name = price_row['supplier_name']
                    
                    col_offset = (i * 3)
                    result_sheet.cell(row=row_num, column=4 + col_offset, value=float(price))
                    result_sheet.cell(row=row_num, column=5 + col_offset, value=int(quantity))
                    result_sheet.cell(row=row_num, column=6 + col_offset, value=str(supplier_name))
            
            # Fill empty cells for remaining suppliers
            for i in range(len(price_results), 3):
                col_offset = (i * 3)
                result_sheet.cell(row=row_num, column=4 + col_offset, value='')
                result_sheet.cell(row=row_num, column=5 + col_offset, value='')
                result_sheet.cell(row=row_num, column=6 + col_offset, value='')
        
        except Exception as e:
            logger.error(f"Error processing row {row_num}: {e}")
            # Fill all cells with empty values on error
            for i in range(3):
                col_offset = (i * 3)
                result_sheet.cell(row=row_num, column=4 + col_offset, value='')
                result_sheet.cell(row=row_num, column=5 + col_offset, value='')
                result_sheet.cell(row=row_num, column=6 + col_offset, value='')
        
        processed_rows += 1
    
    # Save the workbook
    file_name = f"cross_dock_{uuid.uuid4()}.xlsx"
    file_url = save_workbook(wb, file_name)
    
    progress = "100%"
    return progress, file_url

def process_cross_dock_data_from_file(input_file_path: str, platform: str = "emex") -> str:
    """
    Process cross-dock data from an Excel file and generate a new Excel file.
    
    Args:
        input_file_path: Path to the input Excel file
        platform: Platform to query (default: "emex")
        
    Returns:
        str: Path to the generated output file
    """
    logger.info(f"Processing cross-dock data from file {input_file_path}")
    
    # Read input Excel file
    df = pd.read_excel(input_file_path)
    
    # Convert DataFrame to list of dictionaries
    data = df.to_dict('records')
    
    # Process the data
    _, file_url = process_cross_dock_data(data, platform)
    
    # Convert URL to file path
    file_name = os.path.basename(file_url)
    output_file_path = os.path.join(settings.MEDIA_ROOT, 'exports', file_name)
    
    return output_file_path
```

## Expected Output Format

The Excel file will have the following columns:

1. SKU (Brand|Article)
2. Brand
3. Article
4. Best Price 1
5. Quantity 1
6. Supplier Name 1
7. Best Price 2
8. Quantity 2
9. Supplier Name 2
10. Best Price 3
11. Quantity 3
12. Supplier Name 3

For each brand/article pair, we'll query ClickHouse for supplier data and display up to 3 suppliers with the best prices.

## Example of Using Factory-Boy

Here's an example of how we'll use factory-boy to create test data:

```python
import factory
from factory.faker import Faker

class SupplierDataFactory(factory.Factory):
    class Meta:
        model = dict
    
    price = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    quantity = factory.Faker('pyint', min_value=1, max_value=100)
    supplier_name = factory.Faker('company')

class InputDataFactory(factory.Factory):
    class Meta:
        model = dict
    
    Бренд = factory.Faker('random_element', elements=['HYUNDAI/KIA/MOBIS', 'VAG', 'NISSAN', 'SSANGYONG'])
    Артикул = factory.Faker('bothify', text='#######??')

# Usage in tests:
def test_with_factories():
    # Create a batch of supplier data
    supplier_data = SupplierDataFactory.create_batch(3)
    
    # Create input data
    input_data = InputDataFactory.create_batch(5)
    
    # Use in tests
    # ...
```

## Example of Using Pytest Fixtures

```python
import pytest
import pandas as pd

@pytest.fixture
def sample_supplier_data():
    """Fixture providing sample supplier data as a DataFrame."""
    return pd.DataFrame({
        "price": [100.50, 120.75, 90.25],
        "quantity": [5, 10, 3],
        "supplier_name": ["Supplier A", "Supplier B", "Supplier C"]
    })

@pytest.fixture
def sample_input_data():
    """Fixture providing sample input data."""
    return [
        {"Бренд": "HYUNDAI/KIA/MOBIS", "Артикул": "223112e100"},
        {"Бренд": "VAG", "Артикул": "000915105cd"}
    ]

# Usage in tests:
def test_process_data(sample_input_data, sample_supplier_data, mocker):
    # Arrange
    mocker.patch('module.query_supplier_data', return_value=sample_supplier_data)
    
    # Act
    result = process_cross_dock_data(sample_input_data)
    
    # Assert
    assert result is not None
    # more assertions...
```

## Conclusion

This TDD approach ensures that we have comprehensive test coverage and that our implementation meets the requirements before we even start writing the actual code. By following this plan and best practices, we'll create a robust and maintainable solution for generating Excel files with cross-dock data.

The use of pytest, factory-boy, and other testing tools will help us write clean, maintainable tests that properly validate our code's behavior while remaining isolated from external dependencies.
