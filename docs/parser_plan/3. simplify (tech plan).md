# 3. simplify (tech plan)





# Technical Plan for Cross-Dock Functionality Simplification

Let's create a detailed technical plan for implementing the cross-dock functionality in Django. I'll break this down into specific components with code examples that can be easily adapted when you're ready to implement.

## 1. Django Models

First, let's define the models we'll need in the`cross_dock`app:

```python
# cross_dock/models.py
from django.db import models
import uuid

class CrossDockTask(models.Model):
    """Model for tracking cross-dock tasks."""
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('RUNNING', 'Running'),
        ('SUCCESS', 'Success'),
        ('FAILURE', 'Failure'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    result_url = models.URLField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    
    # Store the original request parameters
    input_data = models.JSONField()
    platforms = models.JSONField()
    filename = models.CharField(max_length=255, null=True, blank=True)
    
    def __str__(self):
        return f"CrossDockTask {self.id} - {self.status}"
```

## 2. ClickHouse Connection Service

Let's create a service for ClickHouse connections in the`common`app:

```python
# common/services/clickhouse.py
from clickhouse_driver import Client
from django.conf import settings
import contextlib

@contextlib.contextmanager
def get_clickhouse_client():
    """Context manager for ClickHouse client connections."""
    client = Client(
        settings.CLICKHOUSE_HOST,
        user=settings.CLICKHOUSE_USER,
        password=settings.CLICKHOUSE_PASSWORD
    )
    try:
        yield client
    finally:
        client.disconnect()
```

## 3. Excel Utilities

Create reusable Excel utilities in the`common`app:

```python
# common/utils/excel.py
from openpyxl import Workbook
import os
from django.conf import settings

def create_workbook():
    """Create a new Excel workbook."""
    return Workbook()

def save_workbook(wb, filename):
    """Save workbook to the exports directory."""
    # Create directory if it doesn't exist
    export_dir = os.path.join(settings.MEDIA_ROOT, 'exports')
    os.makedirs(export_dir, exist_ok=True)
    
    # Save the file
    file_path = os.path.join(export_dir, filename)
    wb.save(file_path)
    
    # Return the URL
    return os.path.join(settings.MEDIA_URL, 'exports', filename)
```

## 4. Cross-Dock Service

Now, let's create the core business logic for the cross-dock functionality:

```python
# cross_dock/services/percentage_cross.py
import pandas as pd
import io
import json
from datetime import datetime
from common.services.clickhouse import get_clickhouse_client
from common.utils.excel import create_workbook, save_workbook
import logging

logger = logging.getLogger(__name__)

def process_cross_dock_data(task_id, data, platforms):
    """
    Process cross-dock data and generate Excel file.
    
    Args:
        task_id: The task ID
        data: JSON data containing brand and article information
        platforms: List of platforms to query
        
    Returns:
        tuple: (progress percentage, file URL)
    """
    logger.info(f"Processing cross-dock data for task {task_id}")
    
    # Convert JSON data to DataFrame
    df = pd.read_json(io.StringIO(json.dumps(data)))
    
    # Create workbook
    wb = create_workbook()
    result_sheet = wb.active
    
    # Add headers
    headers = ["SKU", "Бренд", "Артикул", "Лучшая цена 1", "Количество 1", "Название поставщика 1",
               "Лучшая цена 2", "Количество 2", "Название поставщика 2",
               "Лучшая цена 3", "Количество 3", "Название поставщика 3"]
    
    for col_num, header in enumerate(headers, start=1):
        result_sheet.cell(row=1, column=col_num, value=header)
    
    # Get supplier data from ClickHouse
    with get_clickhouse_client() as client:
        # Get suppliers for the platform
        query = f"""
        SELECT DISTINCT dif_id
        FROM sup_stat.sup_list
        WHERE has(lists, '{platforms[0]}')
        """
        result = client.execute(query)
        sups_list = [str(item[0]) for item in result]
        sups = ", ".join(sups_list)
        
        # Process each row
        total_rows = len(df)
        for row_num, row_data in enumerate(df.iterrows(), start=2):
            index, row = row_data
            brand = str(row['Бренд'])
            article = str(row['Артикул'])
            logger.debug(f"Processing row {row_num}: {brand}, {article}")
            
            # Create SKU
            sku = f"{brand}|{article}"
            
            # Write basic data
            result_sheet.cell(row=row_num, column=1, value=sku)
            result_sheet.cell(row=row_num, column=2, value=brand)
            result_sheet.cell(row=row_num, column=3, value=article)
            
            # Query ClickHouse for price data
            # (Simplified for this example - actual query would be more complex)
            price_query = f"""
            SELECT price, quantity, supplier_name
            FROM sup_stat.some_price_table
            WHERE brand = '{brand}' AND article = '{article}'
            AND supplier_id IN ({sups})
            ORDER BY price ASC
            LIMIT 3
            """
            
            try:
                price_results = client.execute(price_query)
                
                # Write price data
                for i, (price, quantity, supplier_name) in enumerate(price_results, start=1):
                    col_offset = 3 + (i-1) * 3
                    result_sheet.cell(row=row_num, column=col_offset + 1, value=price)
                    result_sheet.cell(row=row_num, column=col_offset + 2, value=quantity)
                    result_sheet.cell(row=row_num, column=col_offset + 3, value=supplier_name)
            
            except Exception as e:
                logger.error(f"Error querying price data: {e}")
    
    # Save the workbook
    file_name = f"{task_id}.xlsx"
    file_url = save_workbook(wb, file_name)
    
    return "100%", file_url
```

## 5. Celery Task

Define the Celery task for processing cross-dock data:

```python
# cross_dock/tasks.py
from celery import shared_task
from .models import CrossDockTask
from .services.percentage_cross import process_cross_dock_data
import logging

logger = logging.getLogger(__name__)

@shared_task(bind=True)
def generate_cross_dock_excel(self, task_params):
    """
    Celery task to generate cross-dock Excel file.
    
    Args:
        task_params: Dictionary containing task parameters
        
    Returns:
        dict: Task result with progress and URL
    """
    task_id = self.request.id
    logger.info(f"Starting cross-dock task {task_id}")
    
    try:
        # Get or create task record
        task, created = CrossDockTask.objects.get_or_create(
            id=task_id,
            defaults={
                'status': 'RUNNING',
                'input_data': task_params['data'],
                'platforms': task_params['platforms'],
                'filename': task_params.get('filename', '')
            }
        )
        
        if not created:
            task.status = 'RUNNING'
            task.save()
        
        # Process data
        progress, file_url = process_cross_dock_data(
            task_id, 
            task_params['data'],
            task_params['platforms']
        )
        
        # Update task with result
        task.status = 'SUCCESS'
        task.result_url = file_url
        task.save()
        
        return {'progress': progress, 'url': file_url}
    
    except Exception as e:
        logger.exception(f"Error in cross-dock task: {e}")
        
        # Update task with error
        task = CrossDockTask.objects.get(id=task_id)
        task.status = 'FAILURE'
        task.error_message = str(e)
        task.save()
        
        raise
```

## 6. Django Views

Create the views for the cross-dock functionality:

```python
# cross_dock/views.py
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.views import View
from .tasks import generate_cross_dock_excel
from .models import CrossDockTask
import json
import logging

logger = logging.getLogger(__name__)

def index(request):
    """Main cross-dock page with form."""
    return render(request, "cross_dock/index.html")

def create_task(request):
    """Handle form submission and create a new task."""
    if request.method == "POST":
        try:
            # Extract data from form
            input_data = {
                "brands": request.POST.getlist("brands"),
                "articles": request.POST.getlist("articles"),
                "region": request.POST.get("region"),
                "include_all_suppliers": request.POST.get("include_all_suppliers") == "on"
            }
            
            platforms = {
                "emex": request.POST.get("platform_emex") == "on",
                "autopiter": request.POST.get("platform_autopiter") == "on",
                "exist": request.POST.get("platform_exist") == "on",
                "part_kom": request.POST.get("platform_part_kom") == "on"
            }
            
            filename = request.POST.get("filename", "")
            
            # Create task parameters
            task_params = {
                "data": input_data,
                "platforms": platforms,
                "filename": filename
            }
            
            # Start Celery task
            task = generate_cross_dock_excel.delay(task_params)
            
            # Create task record
            CrossDockTask.objects.create(
                id=task.id,
                input_data=input_data,
                platforms=platforms,
                filename=filename
            )
            
            # Return task status partial
            return render(request, "cross_dock/partials/task_status.html", {
                "task_id": task.id
            })
            
        except Exception as e:
            logger.exception(f"Error creating cross-dock task: {e}")
            return render(request, "cross_dock/partials/error.html", {
                "error": str(e)
            })
    
    # If not POST, redirect to index
    return HttpResponse(status=400)

def task_status(request, task_id):
    """Get the current status of a task."""
    task = get_object_or_404(CrossDockTask, id=task_id)
    
    # If task is complete, render with download link
    if task.status == "SUCCESS":
        return render(request, "cross_dock/partials/task_complete.html", {
            "task": task
        })
    
    # If task failed, render error
    if task.status == "FAILURE":
        return render(request, "cross_dock/partials/task_failed.html", {
            "task": task,
            "error": task.error_message
        })
    
    # Otherwise, render in-progress status with polling
    return render(request, "cross_dock/partials/task_in_progress.html", {
        "task": task
    })

def task_list(request):
    """Show list of all tasks."""
    tasks = CrossDockTask.objects.all()
    return render(request, "cross_dock/task_list.html", {
        "tasks": tasks
    })

```

##

### Template Examples

Here's how the templates would work with HTMX:

**Main Form (index.html)**:

```html
{% extends "core/layouts/blank.html" %}
{% block title %}Cross Dock{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto px-8 py-12">
    <h1 class="text-2xl font-bold mb-6">Cross Dock Query</h1>
    
    <form hx-post="{% url 'cross_dock:create_task' %}" 
          hx-target="#task-status" 
          hx-swap="innerHTML">
        
        <div class="mb-4">
            <label class="block mb-2">Brands</label>
            <input type="text" name="brands" class="w-full p-2 border rounded" 
                   placeholder="Enter brands (comma separated)">
        </div>
        
        <div class="mb-4">
            <label class="block mb-2">Articles</label>
            <input type="text" name="articles" class="w-full p-2 border rounded" 
                   placeholder="Enter articles (comma separated)">
        </div>
        
        <!-- More form fields -->
        
        <div class="mb-4">
            <label class="block mb-2">Platforms</label>
            <div class="flex space-x-4">
                <label><input type="checkbox" name="platform_emex" checked> Emex</label>
                <label><input type="checkbox" name="platform_autopiter" checked> Autopiter</label>
                <label><input type="checkbox" name="platform_exist"> Exist</label>
                <label><input type="checkbox" name="platform_part_kom"> Part-Kom</label>
            </div>
        </div>
        
        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">
            Start Query
        </button>
    </form>
    
    <div id="task-status" class="mt-8">
        <!-- Task status will be loaded here -->
    </div>
</div>
{% endblock %}
```

## **Task Status Partial (partials/task\_status.html)**:

```html
<div class="bg-gray-100 p-4 rounded">
    <h2 class="text-lg font-semibold mb-2">Task Started</h2>
    <p>Task ID: {{ task_id }}</p>
    
    <div hx-get="{% url 'cross_dock:task_status' task_id %}" 
         hx-trigger="load delay:1s" 
         hx-swap="outerHTML">
        <p>Initializing task...</p>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-blue-600 h-2.5 rounded-full" style="width: 10%"></div>
        </div>
    </div>
</div>
```

**Task In Progress Partial (partials/task\_in\_progress.html)**:

```html
<div hx-get="{% url 'cross_dock:task_status' task.id %}" 
     hx-trigger="every 3s" 
     hx-swap="outerHTML">
    <h2 class="text-lg font-semibold mb-2">Processing</h2>
    <p>Status: {{ task.status }}</p>
    <div class="w-full bg-gray-200 rounded-full h-2.5">
        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 50%"></div>
    </div>
</div>
```

**Task Complete Partial (partials/task\_complete.html)**:

```html
<div class="bg-green-100 p-4 rounded">
    <h2 class="text-lg font-semibold mb-2">Task Complete</h2>
    <p>Your cross-dock query has finished successfully!</p>
    <a href="{{ task.result_url }}" class="bg-green-500 text-white px-4 py-2 rounded inline-block mt-2">
        Download Results
    </a>
</div>
```

### 7. URL Configuration

Configure the URLs for the cross-dock app:

```python
# cross_dock/urls.py
from django.urls import path
from . import views

app_name = "cross_dock"
urlpatterns = [
    path("", views.index, name="index"),
    path("create/", views.create_task, name="create_task"),
    path("status/<uuid:task_id>/", views.task_status, name="task_status"),
    path("tasks/", views.task_list, name="task_list"),
]
```

## 8. API Key Authentication (possibly do not use it)

Create a decorator for API key authentication:

```python
# common/decorators.py
from django.http import JsonResponse
from django.conf import settings
import functools

def require_api_key(view_func):
    """
    Decorator to require API key for views.
    Checks for X-API-KEY and App-Key headers.
    """
    @functools.wraps(view_func)
    def wrapper(request, *args, **kwargs):
        api_key = request.headers.get('X-API-KEY')
        app_key = request.headers.get('App-Key')
        
        if api_key != settings.DJANGO_SECRET_KEY:
            return JsonResponse({"error": "Invalid API key"}, status=403)
        
        if app_key != settings.CLIENT_APP_KEY_ADMIN:
            return JsonResponse({"error": "Invalid app key"}, status=403)
        
        return view_func(request, *args, **kwargs)
    
    return wrapper
```

## 9. Settings Configuration

Add the necessary settings to your Django settings file:

```python
# config/django_config/base.py

# ClickHouse settings
+CLICKHOUSE_HOST = os.getenv("CLICKHOUSE_HOST")
+CLICKHOUSE_USER = os.getenv("CLICKHOUSE_USER")
+CLICKHOUSE_PASSWORD = os.getenv("CLICKHOUSE_PASSWORD")

# API authentication
CLIENT_APP_KEY_ADMIN = 'your-app-key-here'

# Media settings for Excel files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Celery settings
CELERY_BROKER_URL = f'redis://{os.environ.get("REDIS_HOST", "localhost")}:{os.environ.get("REDIS_PORT", "6378")}/1'
CELERY_RESULT_BACKEND = f'redis://{os.environ.get("REDIS_HOST", "localhost")}:{os.environ.get("REDIS_PORT", "6378")}/1'
CELERY_TIMEZONE = 'Europe/Moscow'
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60
```

## 10. App Configuration

Configure the cross-dock app:

```python
# cross_dock/apps.py
from django.apps import AppConfig

class CrossDockConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'cross_dock'
    
    def ready(self):
        # Import signals or perform other initialization
        pass
```

## Summary

This technical plan provides a complete blueprint for implementing the cross-dock functionality in Django. The key improvements include:

1. **Simplified Data Flow**: Removed unnecessary async/await patterns
2. **Clear Separation of Concerns**:
   * Models for data storage
   * Services for business logic
   * Views for API endpoints
   * Utilities for common functions
3. **Improved Error Handling**: Consistent try/except blocks with proper logging
4. **Standardized Database Access**: Context managers for database connections
5. **Django Integration**: Leveraging Django's ORM, views, and authentication

When you're ready to implement, you can essentially copy these code examples into the appropriate files in your Django project structure, make any necessary adjustments, and you'll have a clean, maintainable implementation of the cross-dock functionality.
