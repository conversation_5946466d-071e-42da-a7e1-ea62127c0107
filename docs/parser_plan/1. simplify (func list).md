# 1. simplify (func list)

Query:

&#x20;   Our task:
\- concentrate our attention on functionality that has to do with our future cross-dock app (it starts with \`async def start\_percentage\_cross()\` function)
\- go through each function in the chain and simplify it to its bear bones. This includes getting rid of any async stuff. Use context from the .md files to figure out what will be used as a separate \`common\` app when assessing the simplification

But before we do it, make a list of all the functions that have to do with this functionality. So, nothing to do with parsing, but everything to do with cross-dock ClickHouse data extraction and creating the excel file.
At this step, only make a list of all the functions that we need, don't do any simplification yet. It will be our next step. These functions will be the backbone of our new cross-dock app in the Django project.

&#x20;   

Response:



# Functions Related to Cross-Dock Functionality

## API Endpoint Functions

* `start_percentage_cross()`- The main entry point for the cross-dock functionality in the Flask API

## Task Management Functions

* `generate_sql_query_percentage()`- Celery task that handles the cross-dock data processing
* `_generate_sql_query_percentage()`- Inner async function within the Celery task

## Data Processing Functions

* `process_data()`- Main function that processes data for cross-dock functionality
  * Queries ClickHouse for supplier data
  * Processes input data from JSON
  * Creates Excel workbook and populates it with data
  * Returns progress and file name

## ClickHouse Query Functions

* `check_brand_in_region_brand()`- Checks if a brand exists in the region\_brand table
* `load_dataframe_from_db()`- Loads data from ClickHouse into a pandas DataFrame

## Excel File Generation Functions (Cross-dock specific)

* `create_workbook()`- Creates a new Excel workbook
* `get_active_worksheet()`- Returns the active worksheet from a workbook
* `generate_file_name()`- Generates a file name based on task ID
* `save_workbook()`- Saves the workbook to a file

## Configuration and Utility Functions

* `load_price_coefficients_from_yaml()`- Loads price coefficients from YAML file

## Database Connection Functions

* `db_connection()`- Context manager for PostgreSQL database connections
* `get_clickhouse_client()`(from planning file) - Function to get a ClickHouse client connection

## Task Status Functions

* `fetch_tasks_progress()`- Fetches progress of tasks
* `get_tasks_percentage_cross()`- API endpoint to get status of cross-dock tasks

## Authentication Functions

* `require_app_key()`- Decorator for API authentication

## Constants and Configuration Classes

* `ClickInfo`- Class containing ClickHouse connection information

The cross-dock functionality is primarily focused on retrieving data from ClickHouse about suppliers and their prices for specific brand/article combinations, and then presenting this data in an Excel format. The core functionality appears to be in the `process_data()` function, which handles the ClickHouse queries and basic Excel file creation.
