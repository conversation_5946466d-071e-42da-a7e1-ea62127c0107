fail_fast: false

exclude: |
  (?x)(
    /migrations/|
    /.ruff_cache/|
    /.pytest_cache/|
    /.venv/|
    __pypackages__/|
    \.ipynb$
  )

repos:
  # Validate the structure of pyproject.toml
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.24.1
    hooks:
      - id: validate-pyproject

  # Catch common typos across the codebase
  - repo: https://github.com/crate-ci/typos
    rev: v1.32.0
    hooks:
      - id: typos

  # Run Ruff autoformatter
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.12
    hooks:
      - id: ruff-format
        args: [--config=ruff.toml]
        pass_filenames: false # Faster: formats entire project at once

      - id: ruff
        args: [--fix, --exit-non-zero-on-fix, --config=ruff.toml]
        pass_filenames: true # Needed to avoid false positives in lint-only mode

  # Prettier for YAML/JSON5 configs
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, json5]
        additional_dependencies: ["prettier@3.1.0"]
