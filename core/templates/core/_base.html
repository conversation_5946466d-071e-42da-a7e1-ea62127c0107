{% load static %}
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://unpkg.com/htmx.org/dist/htmx.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@5" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">

    <!--
    The title block takes precedence for custom (non-allauth) pages.
    If not defined, it falls back to head_title (used by django-allauth).
    We can now use block title in all our templates, and allauth pages will use head_title.
    -->
    <title>
        {% block title %}
            {% block head_title %}{% endblock %}
        {% endblock %}
    </title>

</head>
<body class="{% block class %}{% endblock %}">

{% include "core/partials/messages.html" %}
{% include "core/partials/header.html" %}

{% block layout %}
{% endblock layout %}

</body>
</html>
