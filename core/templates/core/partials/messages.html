<messages>
    {% if messages %}
        <div x-data="{ showMessage: false }">
            {% for message in messages %}
                <div class="absolute left-0 right-0 max-w-xl mx-auto mt-3 px-4 z-50 ">
                    <div x-cloak
                         class="alert-{{ message.tags }} flex items-center py-3 pl-6 pr-4 bg-blue-500 text-white rounded-lg"
                         role="alert"
                         x-show="showMessage"
                         x-init="setTimeout(() => showMessage = true, 200), setTimeout(() => showMessage = false, 6000)"
                         x-transition:enter="duration-700 ease-out"
                         x-transition:enter-start="opacity-0 -translate-y-5"
                         x-transition:enter-end="opacity-100 translate-y-0"
                         x-transition:leave="duration-200 ease-in"
                         x-transition:leave-start="opacity-100 translate-y-0"
                         x-transition:leave-end="opacity-0 -translate-y-5">
                        <div>
                            <div class="text-lg">{{ message }}</div>
                        </div>
                        <div class="ml-auto cursor-pointer" @click="showMessage = false">
                            <svg fill="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                 viewBox="0 0 24 24" stroke="currentColor" class="w-8 h-8 ml-2">
                                <path d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
</messages>
