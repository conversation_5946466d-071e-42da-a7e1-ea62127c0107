{% extends "core/layouts/blank.html" %}
{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto p-4 md:p-8">
    <header class="mb-8">
        <h1 class="text-3xl font-bold text-base-content">Dashboard</h1>
        <p class="text-base-content/60 mt-2">Manage your operations and access different sections</p>
    </header>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        <!-- Cross-dock Section -->
        <div class="card bg-base-100 shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-0.25 border-1 border-base-300">
            <div class="card-body p-4 md:p-6">
                <div class="flex items-center gap-4">
                    <div class="p-3 rounded-xl bg-primary/10 text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <a href="{% url 'cross_dock:create_task' %}" class="text-current no-underline hover:no-underline"><h2 class="card-title text-lg text-base-content">Cross-dock</h2></a>
                </div>
                <div class="divider my-2"></div>
                <div class="flex flex-col gap-2">
                    <a href="{% url 'cross_dock:create_task' %}" class="btn btn-ghost btn-sm justify-start gap-2 text-base-content/80 hover:text-primary hover:bg-base-200 transition-colors duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Create Task
                    </a>
                    <a href="{% url 'cross_dock:task_list' %}" class="btn btn-ghost btn-sm justify-start gap-2 text-base-content/80 hover:text-primary hover:bg-base-200 transition-colors duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Task List
                    </a>
                </div>
            </div>
        </div>

        <!-- Pricelens Section -->
        <div class="card bg-base-100 shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-0.25 border-1 border-base-300">
            <div class="card-body p-4 md:p-6">
                <div class="flex items-center gap-4">
                    <div class="p-3 rounded-xl bg-info/10 text-info">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <a href="{% url 'pricelens:dashboard' %}" class="text-current no-underline hover:no-underline"><h2 class="card-title text-lg text-base-content">Мониторинг прихода прайс-листов</h2></a>
                </div>
                <div class="divider my-2"></div>
                <div class="flex flex-col gap-2">
                    <a href="{% url 'pricelens:dashboard' %}" class="btn btn-ghost btn-sm justify-start gap-2 text-base-content/80 hover:text-info hover:bg-base-200 transition-colors duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Перейти к панели
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
