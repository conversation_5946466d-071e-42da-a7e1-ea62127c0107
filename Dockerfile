# Stage 1: Base build stage
FROM python:3.12-slim AS builder

# Create the app directory
RUN mkdir /app

# Set the working directory
WORKDIR /app

# Set environment variables to optimize Python
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install dependencies first for caching benefit
RUN pip install --upgrade pip
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Stage 2: Production stage
FROM python:3.12-slim

RUN useradd -m -r appuser && \
   mkdir /app && \
   chown -R appuser /app

# Copy the Python dependencies from the builder stage
COPY --from=builder /usr/local/lib/python3.12/site-packages/ /usr/local/lib/python3.12/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# Set the working directory
WORKDIR /app

RUN mkdir -p /app/media

# Copy application code
COPY --chown=appuser:appuser . .

# Set environment variables to optimize Python
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Switch to non-root user
#USER appuser

# Expose the application port
EXPOSE 8061

CMD ["sh", "-c", "export DJANGO_SETTINGS_MODULE=config.django_config.production && python manage.py collectstatic --noinput && python manage.py migrate --noinput && python -m gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers 6 --threads 4 --worker-class gthread --timeout 120"]


## Make entry file executable
#RUN chmod +x  /app/entrypoint.prod.sh
#
## Start the application using Gunicorn
#CMD ["/app/entrypoint.prod.sh"]
