{% extends "core/layouts/blank.html" %}
{% block title %}Task Details - {{ task.id }}{% endblock %}
{% block content %}

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
<!-- Breadcrumbs -->
    <div class="text-sm breadcrumbs mb-6">
        <ul>
            <li><a href="{% url 'index' %}" class="text-primary">Home</a></li>
            <li><a href="{% url 'cross_dock:task_list' %}" class="text-primary">Task List</a></li>
            <li class="text-gray-500">Task Info</li>
        </ul>
    </div>

    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
            <a href="{% url 'cross_dock:task_list' %}" class="btn btn-ghost btn-circle btn-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
            </a>
            <h1 class="text-2xl font-bold">Task Info</h1>
        </div>
        {% if task.status == "SUCCESS" and task.result_url %}
        <a href="{{ task.result_url }}" class="btn btn-success gap-2">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Download Result
        </a>
        {% endif %}
    </div>

    <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body p-0">
            <div class="px-6 py-5 bg-base-200">
                <h3 class="card-title text-lg">Task Information</h3>
                <p class="text-sm opacity-70">Details about the cross-dock task.</p>
            </div>
            <div class="divider m-0"></div>
            <div class="overflow-x-auto">
                <table class="table
                    [&>tbody>tr>th]:bg-base-100 [&>tbody>tr>th]:font-medium [&>tbody>tr>th]:text-opacity-60 [&>tbody>tr>th]:w-1/3
                    [&>tbody>tr>td]:bg-base-100 [&>tbody>tr>td]:whitespace-nowrap [&>tbody>tr>td]:overflow-hidden [&>tbody>tr>td]:text-ellipsis">
                    <tbody>
                        <tr>
                            <th>Task ID</th>
                            <td class="flex items-center gap-2">
                                <span class="truncate">{{ task.id }}</span>
                                <div class="relative inline-flex items-center">
                                    <button
                                        class="btn btn-ghost btn-xs btn-circle"
                                        style="cursor:copy"
                                        title="Copy ID"
                                        x-data="{ copied: false }"
                                        x-on:click="
                                            const textToCopy = '{{ task.id }}';
                                            if (navigator.clipboard) {
                                                navigator.clipboard.writeText(textToCopy).then(() => {
                                                    copied = true;
                                                    setTimeout(() => copied = false, 1500);
                                                }).catch(() => {
                                                    // Fallback for when clipboard API fails
                                                    copyToClipboardFallback(textToCopy);
                                                    copied = true;
                                                    setTimeout(() => copied = false, 1500);
                                                });
                                            } else {
                                                // Fallback for older browsers
                                                copyToClipboardFallback(textToCopy);
                                                copied = true;
                                                setTimeout(() => copied = false, 1500);
                                            }"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                        </svg>
                                        <span
                                            x-show="copied"
                                            x-cloak
                                            x-transition
                                            class="absolute left-full ml-2 whitespace-nowrap badge badge-soft badge-success badge-sm"
                                        >
                                            <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                                <g fill="currentColor" stroke-linejoin="miter" stroke-linecap="butt">
                                                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-linecap="square" stroke-miterlimit="10" stroke-width="2"></circle>
                                                    <polyline points="7 13 10 16 17 8" fill="none" stroke="currentColor" stroke-linecap="square" stroke-miterlimit="10" stroke-width="2"></polyline>
                                                </g>
                                            </svg>
                                            <span class="ml-1">Скопировано!</span>
                                        </span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                {% if task.status == "SUCCESS" %}
                                    <span class="badge badge-success gap-1">
                                        <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        Success
                                    </span>
                                {% elif task.status == "RUNNING" %}
                                    <span class="badge badge-info gap-1">
                                        <span class="loading loading-spinner loading-xs"></span>
                                        Running
                                    </span>
                                {% elif task.status == "PENDING" %}
                                    <span class="badge badge-warning gap-1">
                                        <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Pending
                                    </span>
                                {% else %}
                                    <span class="badge badge-error gap-1">
                                        <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                        Failed
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>File Name</th>
                            <td class="truncate max-w-xs" title="{{ task.filename }}">{{ task.filename }}</td>
                        </tr>
                        <tr>
                            <th>Supplier Group</th>
                            <td class="truncate max-w-xs" title="{{ task.supplier_group }}">{{ task.supplier_group }}</td>
                        </tr>
                        <tr>
                            <th>Created By</th>
                            <td>
                                <a href="{% url 'profile' task.user.username%}" class="link link-hover link-primary">
                                    {{ task.user.profile.name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ task.created_at|date:"d.m.Y H:i:s" }}</td>
                        </tr>
                        <tr>
                            <th>Execution Time</th>
                            <td>{{ task.execution_time }}</td>
                        </tr>
                        {% if task.status == "FAILURE" and task.error_message %}
                        <tr>
                            <th class="text-error">Error Message</th>
                            <td>
                                <div class="bg-error bg-opacity-10 p-3 rounded-box overflow-auto max-h-40">
                                    <pre class="text-xs font-mono">{{ task.error_message }}</pre>
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Comments Section -->
    <div class="card bg-base-100 shadow-xl mb-8">
        <div class="card-body p-0">
            <!-- Comments List -->
            <div class="p-6">
                {% if comments %}
                    <ul class="space-y-6">
                        {% for comment in comments %}
                        <li class="chat chat-start">
                            <div class="chat-image avatar">
                                <div class="w-10 h-10 rounded-full">
                                    {% if comment.user %}
                                        <img src="{{ comment.user.profile.avatar }}" alt="{{ comment.user.profile.name }}" />
                                    {% else %}
                                        <div class="bg-gray-300 text-gray-600 flex items-center justify-center h-full">
                                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path>
                                            </svg>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="chat-header">
                                {{ comment.user.profile.name|default:"Anonymous" }}
                                <time class="text-xs opacity-50 ml-2">{{ comment.created_at|date:"d.m.Y H:i" }}</time>
                            </div>
                            <div class="chat-bubble bg-base-200 text-base-content">
                                {{ comment.text|linebreaksbr }}
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="text-center py-8 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 opacity-40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <p>No comments yet. Be the first to comment!</p>
                    </div>
                {% endif %}
            </div>
            
            <!-- Comment Form -->
            {% if user.is_authenticated %}
            <div class="border-t border-base-300 p-6 bg-base-200">
                <form method="post" action="{% url 'cross_dock:task_detail' task_id=task.id %}" class="space-y-4">
                    {% csrf_token %}
                    <div class="form-control">
                        <textarea 
                            id="comment_text" 
                            name="comment_text" 
                            rows="3" 
                            class="textarea textarea-bordered w-full"
                            placeholder="Type your comment here..."
                            required
                        ></textarea>
                    </div>
                    <div class="flex justify-start">
                        <button type="submit" class="btn btn-primary gap-2">
                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Add Comment
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    function copyToClipboardFallback(text) {
        // Create a temporary textarea element
        const textarea = document.createElement('textarea');
        textarea.value = text;
        
        // Make the textarea out of viewport
        textarea.style.position = 'fixed';
        textarea.style.opacity = 0;
        
        // Add to document and select text
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            // Execute copy command
            const successful = document.execCommand('copy');
            if (!successful) {
                console.warn('Fallback: Copying text was unsuccessful');
                // Alternative fallback: Show the text in a prompt
                prompt('Copy to clipboard: Ctrl+C, Enter', text);
            }
        } catch (err) {
            console.error('Fallback: Could not copy text', err);
            // Last resort: Show the text in a prompt
            prompt('Copy to clipboard: Ctrl+C, Enter', text);
        }
        
        // Clean up
        document.body.removeChild(textarea);
    }
</script>

{% endblock content %}
