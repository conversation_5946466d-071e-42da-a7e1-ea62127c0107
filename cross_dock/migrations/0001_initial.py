# Generated by Django 5.2 on 2025-08-04 06:04

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CrossDockTask",
            fields=[
                ("created_at", models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("RUNNING", "Running"),
                            ("SUCCESS", "Success"),
                            ("FAILURE", "Failure"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("result_url", models.URLField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True, null=True)),
                (
                    "supplier_group",
                    models.CharField(
                        blank=True,
                        choices=[("Группа для проценки ТРЕШКА", "Группа для проценки ТРЕШКА"), ("ОПТ-2", "ОПТ-2")],
                        help_text="Supplier group used for this task",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("filename", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this task",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="cross_dock_tasks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TaskComment",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("text", models.TextField(help_text="Comment text")),
                (
                    "task",
                    models.ForeignKey(
                        help_text="Task this comment belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="cross_dock.crossdocktask",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this comment",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="cross_dock_comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="crossdocktask",
            index=models.Index(fields=["status"], name="cross_dock__status_87f0ba_idx"),
        ),
        migrations.AddIndex(
            model_name="crossdocktask",
            index=models.Index(fields=["created_at"], name="cross_dock__created_0899fc_idx"),
        ),
        migrations.AddIndex(
            model_name="crossdocktask",
            index=models.Index(fields=["supplier_group"], name="cross_dock__supplie_66846e_idx"),
        ),
        migrations.AddIndex(
            model_name="taskcomment",
            index=models.Index(fields=["task"], name="cross_dock__task_id_4e72da_idx"),
        ),
        migrations.AddIndex(
            model_name="taskcomment",
            index=models.Index(fields=["user"], name="cross_dock__user_id_5b0076_idx"),
        ),
        migrations.AddIndex(
            model_name="taskcomment",
            index=models.Index(fields=["created_at"], name="cross_dock__created_6f7e0d_idx"),
        ),
    ]
