# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    ".venv",
    "**/migrations/**",
]

# Same as your current setting
line-length = 120
indent-width = 4

# Match your Python version
target-version = "py312"

[lint]
# Expanded rule selection
select = [
    "E",    # pycodestyle errors
    "F",    # Pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "UP",   # pyupgrade
    "N",    # pep8-naming
    "SIM",  # flake8-simplify
    "TCH",  # type-checking
    "RUF",  # Ruff-specific rules
]

# Common rules to ignore
ignore = [
    "E203",     # Whitespace before ':' (conflicts with Black)
    "E501",     # Line too long (handled by formatter)
    "RUF012",   # Mutable class attributes should be annotated with `typing.ClassVar`
    "RUF005",   # Unnecessary use of f-string when no interpolation is present
    "SIM105",   # Use `contextlib.suppress(...)` instead of try/except/pass
    "SIM117",   # Use a single `with` statement instead of multiple nested ones
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Per-file rule exceptions
[lint.per-file-ignores]
"__init__.py" = ["F401"]  # Ignore unused imports in __init__ files
"tests/*" = ["E501"]      # Ignore line length in test files
"*/migrations/*" = ["E501", "F401", "N806"]  # Ignore certain rules in Django migrations

# Import sorting configuration for Django projects
[lint.isort]
known-first-party = ["core", "accounts"]  # Your Django apps
known-third-party = ["django", "allauth", "django_htmx"]
# Use only standard section names
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"