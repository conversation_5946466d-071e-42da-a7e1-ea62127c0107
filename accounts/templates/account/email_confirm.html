{% extends 'core/layouts/box.html' %}
{% load i18n %}

{% block title %}{% trans "Confirm Email" %}{% endblock title %}

{% block content %}
<div class="w-full max-w-md mx-auto text-center">
    <!-- Success State -->
    {% if confirmation %}
        <div class="mb-8">
            <div class="flex justify-center mb-4">
                <div class="p-3 rounded-full bg-primary/10 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
            </div>
            <h1 class="text-2xl font-bold text-base-content">{% trans "Confirm Email Address" %}</h1>
            <p class="text-base-content/70 mt-2">
                {% blocktrans with email=confirmation.email_address.email %}
                    Please confirm that <span class="font-medium">{{ email }}</span> is your email address.
                {% endblocktrans %}
            </p>
        </div>

        <form method="post" action="{% url 'account_confirm_email' confirmation.key %}" class="space-y-4">
            {% csrf_token %}
            <button type="submit" class="btn btn-primary w-full">
                {% trans "Confirm Email" %}
            </button>
        </form>
    
    <!-- Error State -->
    {% else %}
        <div class="mb-8">
            <div class="flex justify-center mb-4">
                <div class="p-3 rounded-full bg-error/10 text-error">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
            </div>
            <h1 class="text-2xl font-bold text-base-content">{% trans "Invalid or Expired Link" %}</h1>
            <p class="text-base-content/70 mt-2">
                {% url 'account_email' as email_url %}
                {% blocktrans %}
                    This email confirmation link has expired or is invalid. Please 
                    <a href="{{ email_url }}" class="font-medium text-primary hover:text-primary/80">issue a new email confirmation request</a>.
                {% endblocktrans %}
            </p>
        </div>
    {% endif %}

    <!-- Back to Home Link -->
    <div class="mt-8 text-center">
        <a href="{% url 'index' %}" class="text-sm font-medium text-primary hover:text-primary/80 inline-flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            {% trans "Back to Home" %}
        </a>
    </div>
</div>
{% endblock content %}