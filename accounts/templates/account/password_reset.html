{% extends "core/layouts/box.html" %}
{% load widget_tweaks %}

{% block title %}Reset Your Password{% endblock title %}

{% block content %}
<div class="w-full max-w-md mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="flex justify-center mb-4">
            <div class="p-3 rounded-full bg-primary/10 text-primary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
            </div>
        </div>
        <h1 class="text-2xl font-bold text-base-content">Forgot your password?</h1>
        <p class="text-base-content/70 mt-2">Enter your email and we'll send you a link to reset your password</p>
    </div>

    {% if form.errors %}
        <div class="alert alert-error mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Please correct the errors below.</span>
        </div>
    {% endif %}

    <form method="POST" class="space-y-6">
        {% csrf_token %}
        
        <!-- Email Field -->
        <div class="form-control w-full">
            <label class="label mb-1" for="{{ form.email.id_for_label }}">
                <span class="label-text">Email address</span>
            </label>
            {{ form.email|add_class:"input input-bordered w-full" }}
            {% if form.email.errors %}
                <p class="mt-1 text-sm text-error">{{ form.email.errors.0 }}</p>
            {% endif %}
        </div>

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary w-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Send Reset Link
        </button>
    </form>

    <!-- Back to Login Link -->
    <div class="mt-6 text-center text-sm">
        <p class="text-base-content/70">
            Remember your password?
            <a href="{% url 'account_login' %}" class="font-medium text-primary hover:text-primary/80">
                Sign in
            </a>
        </p>
    </div>
</div>
{% endblock content %}
