# Django settings
DJ<PERSON><PERSON><PERSON>_SECRET_KEY=super-secret-key
DJANGO_DEBUG=False
DJANGO_ENVIRONMENT=production/staging
DJANGO_ALLOWED_HOSTS=127.0.0.1,localhost,**************
DJANGO_CSRF_TRUSTED_ORIGINS=http://127.0.0.1:8061,http://localhost:8061,http://**************:8061
DJANGO_SETTINGS_MODULE=config.django_config.production  # production/staging

# PostgreSQL settings
POSTGRES_ENGINE=postgresql_psycopg2
POSTGRES_DB=admin2_db
POSTGRES_USER=someuser
POSTGRES_PASSWORD=somepassword
POSTGRES_HOST=db  # or 127.0.0.1
POSTGRES_PORT=5432


# ClickHouse settings
CLICKHOUSE_HOST=**************
CLICKHOUSE_USER=someuser
CLICKHOUSE_PASSWORD=somepassword

# Redis settings
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Other settings
ALLOWED_HOSTS=localhost,127.0.0.1,**************
EXTERNAL_DJANGO_ADMIN_URL=http://************
PRICELENS_FILE_SERVER_URL=http://**************