{% extends "core/layouts/blank.html" %}

{% block title %}Pricelens: Очередь расследований{% endblock %}

{% block content %}
<div class="container mx-auto p-4 md:p-6">

    <div class="text-sm breadcrumbs mb-6">
        <ul>
            <li><a href="{% url 'index' %}" class="text-primary">Главная</a></li>
            <li><a href="{% url 'pricelens:dashboard' %}" class="text-primary">Мониторинг прихода прайс-листов</a></li>
            <li class="text-gray-500">Очередь расследований</li>
        </ul>
    </div>

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold">Очередь расследований</h1>
        <div class="tabs tabs-lift">
            <a href="?status=all" class="tab {% if current_status == 'all' %}tab-active{% endif %}">Все</a>
            {% for value, label in status_choices %}
                <a href="?status={{ value }}" class="tab {% if current_status == value|stringformat:'s' %}tab-active{% endif %}">
                    <span class="{% if value == 1 %}text-success{% elif value == 2 %}text-error{% endif %}">
                        {{ label|capfirst }}
                    </span>
                </a>
            {% endfor %}
        </div>
    </div>

    <!-- Investigation Stats -->
    <div class="stats shadow w-full mb-6">
        {% for stat in investigation_stats %}
            <div class="stat">
                <div class="stat-title">{{ stat.label }}</div>
                <div class="stat-value"><a href="{% url 'pricelens:queue' %}?status={{ stat.value }}">{{ stat.count }}</a></div>
            </div>
        {% endfor %}
    </div>

    <div class="card bg-base-100 shadow-xl">
        <div class="card-body p-4">
            <div class="overflow-x-auto">
                <table class="table w-full">
                    <thead>
                        <tr class="[&>th]:bg-base-200 [&>th]:text-xs [&>th]:font-medium [&>th]:uppercase [&>th]:tracking-wider [&>th]:py-2 [&>th]:px-3">
                            <th>Время события</th>
                            <th>ID Поставщика</th>
                            <th>Имя поставщика</th>
                            <th>Причина ошибки</th>
                            {# <th>Этап</th> #}
                            <th>Статус</th>
                            <th>Заметка</th>
                            <th>Действия</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in investigations %}
                        <tr class="hover:bg-base-300">
                            <td>{{ item.event_dt|date:"Y-m-d H:i:s" }}</td>
                            <td>{{ item.supplier.supid }}</td>
                            <td>{{ item.supplier.name }}</td>
                            <td><span class="badge badge-ghost">{{ item.fail_reason.name }}</span></td>
                            {# <td><span class="badge badge-outline">{{ item.stage }}</span></td> #}
                            <td>
                                <span class="badge
                                    {% if item.status == 0 %}badge-warning{% endif %}
                                    {% if item.status == 1 %}badge-success{% endif %}
                                    {% if item.status == 2 %}badge-error{% endif %}
                                ">{{ item.get_status_display }}</span>
                            </td>
                            <td class="text-sm text-base-content/70">{{ item.note|truncatechars:50 }}</td>
                            <td>

                                {% if item.status == 0 %}
                                    <a href="{% url 'pricelens:investigate' pk=item.id %}" class="btn btn-sm btn-primary">
                                        Расследовать
                                {% else %}
                                    <a href="{% url 'pricelens:investigate' pk=item.id %}" class="btn btn-sm btn-outline">
                                        Подробнее
                                {% endif %}
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-base-content/50">Нет расследований, соответствующих этому фильтру.</div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="flex flex-col md:flex-row justify-between items-center mt-4 px-6 py-3 gap-4">
            <div class="text-sm">
                Показано с {{ page_obj.start_index }} по {{ page_obj.end_index }} из {{ paginator.count }} записей
            </div>
            <div class="join">
                {% if page_obj.has_previous %}
                    <a href="?page=1{% if query_params %}&amp;{{ query_params }}{% endif %}" class="join-item btn btn-sm">Первая</a>
                    <a href="?page={{ page_obj.previous_page_number }}{% if query_params %}&amp;{{ query_params }}{% endif %}" class="join-item btn btn-sm">«</a>
                {% else %}
                    <button class="join-item btn btn-sm btn-disabled">Первая</button>
                    <button class="join-item btn btn-sm btn-disabled">«</button>
                {% endif %}

                {% for num in paginator.page_range %}
                    {% if page_obj.number == num %}
                        <a href="?page={{ num }}{% if query_params %}&amp;{{ query_params }}{% endif %}" class="join-item btn btn-sm btn-active">{{ num }}</a>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if query_params %}&amp;{{ query_params }}{% endif %}" class="join-item btn btn-sm">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if query_params %}&amp;{{ query_params }}{% endif %}" class="join-item btn btn-sm">»</a>
                    <a href="?page={{ paginator.num_pages }}{% if query_params %}&amp;{{ query_params }}{% endif %}" class="join-item btn btn-sm">Последняя</a>
                {% else %}
                    <button class="join-item btn btn-sm btn-disabled">»</button>
                    <button class="join-item btn btn-sm btn-disabled">Последняя</button>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
