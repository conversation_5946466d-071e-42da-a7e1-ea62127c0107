{% extends "core/layouts/blank.html" %}

{% block title %}Мониторинг прихода прайс-листов{% endblock %}

{% block content %}
<div class="container mx-auto p-4 md:p-6">

    <!-- Breadcrumbs -->
    <div class="text-sm breadcrumbs mb-6">
        <ul>
            <li><a href="{% url 'index' %}" class="text-primary">Главная</a></li>
            <li class="text-gray-500">Мониторинг прихода прайс-листов</li>
        </ul>
    </div>

    <h1 class="text-3xl font-bold mb-6">Мониторинг прихода прайс-листов</h1>

    <!-- Phase 1: Summary Stats -->
    <div class="stats shadow w-full mb-6 border border-transparent hover:border-primary-content transition-colors duration-300">
        <div class="stat">
            <div class="stat-figure text-error">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                     class="inline-block w-8 h-8 stroke-current">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
            </div>
            <div class="stat-title">Ошибки за вчера</div>
            <div class="stat-value text-error">{{ summary.failures }}</div>
            <div class="stat-desc">
                <a href="#" onclick="supplier_modal.showModal()" class="link link-hover">
                    У {{ summary.supplier_count }} поставщиков
                </a>
            </div>
        </div>
    </div>
    <!-- End of Phase 1 -->

    <!-- Supplier List Modal -->
    <dialog id="supplier_modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Поставщики с ошибками за вчера</h3>
            <div class="py-4">
                <ul class="list-disc list-inside">
                    {% for supplier in summary.suppliers_with_errors %}
                        <li>{{ supplier.supplier__supid }} - {{ supplier.supplier__name }}</li>
                    {% endfor %}
                </ul>
            </div>
            <div class="modal-action">
                <form method="dialog">
                    <button class="btn">Закрыть</button>
                </form>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop"><button>close</button></form>
    </dialog>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Phase 1: Top Reasons Card -->
        <div class="md:col-span-1">
            <div class="card bg-base-100 shadow-xl h-full border border-transparent hover:border-primary-content hover:bg-base-200/70 transition-colors duration-300">
                <div class="card-body">
                    <h2 class="card-title">Топ 5 причин ошибок за вчера</h2>
                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                            <tr>
                                <th>Причина</th>
                                <th>Кол-во</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for reason in top_reasons %}
                                <tr>
                                    <td><span class="badge badge-ghost">{{ reason.fail_reason__name }}</span></td>
                                    <td>{{ reason.cnt }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="card-actions justify-end mt-4">
                        <a href="{% url 'pricelens:queue' %}" class="btn btn-primary btn-sm">Расследовать ошибки &rarr;</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End of Phase 1 -->

        <!-- Phase 2: Cadence & Anomaly Cards -->
        <div class="md:col-span-2">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Cadence Buckets -->
                <div class="lg:col-span-1">
                    <div class="card bg-base-100 shadow-xl h-full border border-transparent hover:border-primary-content transition-colors duration-300">
                        <div class="card-body">
                            <h2 class="card-title">
                                <a href="{% url 'pricelens:cadence' %}?bucket=all" class="link link-hover">Ритмичность</a>
                                <label for="cadence-explanation-drawer"
                                       class="drawer-button btn btn-xs btn-ghost btn-circle">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         class="inline-block w-4 h-4 stroke-current">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </label>
                            </h2>
                            <div class="stats stats-vertical">
                                {% for bucket in buckets %}
                                    <div class="stat">
                                        <div class="stat-title">
                                            <a href="{% url 'pricelens:cadence' %}?bucket={{ bucket.value }}" class="link link-hover"
                                               title="{{ bucket.tooltip }}" style="text-decoration: underline dotted;cursor:help; text-underline-offset: 3px;">
                                                {{ bucket.label }}
                                            </a>
                                        </div>
                                        <div class="stat-value">
                                            <a href="{% url 'pricelens:cadence' %}?bucket={{ bucket.value }}" class="link link-hover">{{ bucket.count }}</a>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Anomalies Table -->
                <div class="lg:col-span-2">
                    <div class="card bg-base-100 shadow-xl h-full border border-transparent hover:border-primary-content hover:bg-base-200/70 transition-colors duration-300">
                        <div class="card-body">
                            <h2
                                    class="card-title"
                                    title="Текущая задержка в поставке прайс-листа более чем в два раза превышает обычный (медианный) интервал"
                                    style="text-decoration: underline dotted;cursor:help; text-underline-offset: 3px;">
                                Аномалии
                            </h2>
                            <div class="overflow-x-auto">
                                <table class="table w-full">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Имя</th>
                                        <th>Дней с посл. прайс-листа</th>
                                        <th>Норма (дней)</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for anomaly in anomalies %}
                                        <tr>
                                            <td>{{ anomaly.supplier.supid }}</td>
                                            <td>{{ anomaly.supplier.name }}</td>
                                            <td><span
                                                    class="badge badge-warning">{{ anomaly.days_since_last }}</span>
                                            </td>
                                            <td>{{ anomaly.median_gap_days }}</td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End of Phase 2 -->
    </div>

    {% include "pricelens/partials/cadence_explanation_drawer.html" %}

</div>
{% endblock %}

