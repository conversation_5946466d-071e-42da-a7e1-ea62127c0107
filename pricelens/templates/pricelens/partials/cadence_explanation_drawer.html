<div class="drawer drawer-end fixed inset-0 z-50">
    <input id="cadence-explanation-drawer" type="checkbox" class="drawer-toggle"/>
    <div class="drawer-content">
    </div>
    <div class="drawer-side">
        <label for="cadence-explanation-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
        <div class="p-6 w-11/12 md:w-1/2 max-w-lg min-h-full bg-base-100 text-base-content space-y-6">
            <!-- Drawer content here -->
            <div class="flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="text-2xl font-bold">Как рассчитывается ритмичность?</h3>
            </div>

            <p class="text-base-content/80">
                Цель этого расчета — ответить на вопрос: <strong>"Насколько ритмичен и предсказуем этот
                поставщик?"</strong>
                Расчет основан на анализе <strong>промежутков (в днях)</strong> между успешными поставками прайс-листов.
            </p>

            <!-- Step 1 -->
            <div class="card card-compact bg-base-200/50">
                <div class="card-body">
                    <div class="flex items-center gap-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                        <h4 class="card-title text-secondary">Шаг 1: Поиск успешных поставок</h4>
                    </div>
                    <p class="pl-8 text-sm text-base-content/70">Сначала мы собираем историю всех дней за последние 180 дней, когда поставщик успешно прислал нам прайс-лист.</p>
                </div>
            </div>

            <!-- Step 2 -->
            <div class="card card-compact bg-base-200/50">
                <div class="card-body">
                    <div class="flex items-center gap-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>
                        <h4 class="card-title text-secondary">Шаг 2: Расчет промежутков</h4>
                    </div>
                    <p class="pl-8 text-sm text-base-content/70">Далее, для каждого поставщика мы берем его список успешных дат и вычисляем количество дней <em>между</em> каждой из этих дат. Если поставщик присылал файлы 10-го, 13-го и 17-го июля, то промежутки между ними составляют <code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">[3 дня, 4 дня]</code>. Этот массив промежутков — самая важная информация для анализа.</p>
                </div>
            </div>

            <!-- Step 3 -->
            <div class="card card-compact bg-base-200/50">
                <div class="card-body">
                    <div class="flex items-center gap-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>
                        <h4 class="card-title text-secondary">Шаг 3: Ключевые статистические показатели</h4>
                    </div>
                    <div class="pl-8 text-sm text-base-content/70 space-y-3">
                        <p><strong>1. Медиана (<code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">med_gap</code>):</strong> Это "типичное" количество дней между поставками.</p>
                        <p><strong>2. <a href="https://ru.wikipedia.org/wiki/%D0%A1%D1%80%D0%B5%D0%B4%D0%BD%D0%B5%D0%BA%D0%B2%D0%B0%D0%B4%D1%80%D0%B0%D1%82%D0%B8%D1%87%D0%B5%D1%81%D0%BA%D0%BE%D0%B5_%D0%BE%D1%82%D0%BA%D0%BB%D0%BE%D0%BD%D0%B5%D0%BD%D0%B8%D0%B5" target="_blank" class="link">Стандартное отклонение</a> (<code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">sd_gap</code>):</strong> Эта метрика измеряет, насколько "разбросаны" или нестабильны промежутки. Низкое значение означает, что поставщик очень последователен. Высокое значение — что поставки происходят хаотично.</p>
                        <p><strong>3. Процент "плохих" промежутков:</strong> Мы считаем промежуток "плохим", если он более чем в два раза превышает типичный (медианный) промежуток для этого поставщика. Этот показатель помогает игнорировать единичные сбои.</p>
                    </div>
                </div>
            </div>

            <!-- Step 4 -->
            <div class="card card-compact bg-base-200/50">
                <div class="card-body">
                    <div class="flex items-center gap-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A2 2 0 013 8v5a2 2 0 002 2h5M9 7h.01M12 7h.01M15 7h.01M9 10h.01M12 10h.01M15 10h.01M9 13h.01M12 13h.01M15 13h.01" /></svg>
                        <h4 class="card-title text-secondary">Шаг 4: Логика распределения по категориям</h4>
                    </div>
                    <div class="pl-8 text-sm text-base-content/70 space-y-3">
                        <p><strong>1. Мертвые:</strong> Если от поставщика не было поставок 28 дней или более.<br><code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">days_since_last >= 28</code></p>
                        <p><strong>2. Стабильные:</strong> Если поставщик не "мертвый" и соответствует <strong>хотя бы одному</strong> из этих критериев:</p>
                        <ul class="list-disc list-inside pl-4 space-y-1 mt-1">
                            <li>Его общая нестабильность (стандартное отклонение) не превышает его типичный интервал.<br><code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">sd_gap <= med_gap * 1.0</code></li>
                            <li>Или, у него очень мало серьезных сбоев (менее 20% "плохих" промежутков).<br><code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">bad_gap_percentage < 20%</code></li>
                        </ul>
                        <p><strong>3. Нестабильные:</strong> Если поставщик не "мертвый" и не прошел ни одну из проверок на "стабильность". Это указывает на частые и значительные сбои в ритме поставок.</p>
                    </div>
                </div>
            </div>

            <div class="divider">Примеры</div>

            <div class="space-y-4">
                <div class="p-4 border border-base-300 rounded-lg">
                    <p class="font-medium">Поставщик А: Надежный работник (с одним сбоем)</p>
                    <ul class="list-disc list-inside text-sm space-y-1 mt-2 text-base-content/70">
                        <li><strong>Сценарий:</strong> Поставляет почти каждый день, но один раз был долгий перерыв (например, из-за проблемы на нашей стороне).</li>
                        <li><strong>Промежутки:</strong> <code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">[1, 1, 1, 1, 21, 1, 1, 1, 1]</code> (9 промежутков)</li>
                        <li><strong>Медианный промежуток (<code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">med_gap</code>):</strong> 1 день.</li>
                        <li><strong>Стандартное отклонение (<code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">sd_gap</code>):</strong> ~6.2 дня (очень высокое из-за одного выброса).</li>
                        <li><strong>Проверка 1 (по отклонению):</strong> 6.2 <= 1 * 1.0? <span class="text-error font-bold">Нет.</span></li>
                        <li><strong>Проверка 2 (по выбросам):</strong>
                            <ul class="list-inside list-[circle] pl-4">
                                <li>"Плохие" промежутки (те, что > 2 * med_gap): только один - <code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">21</code>.</li>
                                <li>Процент плохих: (1 / 9) * 100 = 11.1%.</li>
                                <li>11.1% < 20%? <span class="text-success font-bold">Да.</span></li>
                            </ul>
                        </li>
                        <li><strong>Результат:</strong> Так как вторая проверка пройдена, поставщик <span class="font-bold text-success">Стабильный</span>.</li>
                    </ul>
                </div>
                <div class="p-4 border border-base-300 rounded-lg">
                    <p class="font-medium">Поставщик Б: Хаотичный барабанщик</p>
                    <ul class="list-disc list-inside text-sm space-y-1 mt-2 text-base-content/70">
                        <li><strong>Сценарий:</strong> Поставляет файлы без какой-либо предсказуемой системы.</li>
                        <li><strong>Промежутки:</strong> <code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">[1, 7, 2, 1, 8, 1, 9]</code> (7 промежутков)</li>
                        <li><strong>Медианный промежуток (<code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">med_gap</code>):</strong> 2 дня.</li>
                        <li><strong>Стандартное отклонение (<code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">sd_gap</code>):</strong> ~3.4 дня (высокое).</li>
                        <li><strong>Проверка 1 (по отклонению):</strong> 3.4 <= 2 * 1.0? <span class="text-error font-bold">Нет.</span></li>
                        <li><strong>Проверка 2 (по выбросам):</strong>
                             <ul class="list-inside list-[circle] pl-4">
                                <li>"Плохие" промежутки (те, что > 4): <code class="font-mono bg-base-300/50 rounded px-1.5 py-0.5 text-sm">[7, 8, 9]</code> (всего 3).</li>
                                <li>Процент плохих: (3 / 7) * 100 = 42.8%.</li>
                                <li>42.8% < 20%? <span class="text-error font-bold">Нет.</span></li>
                            </ul>
                        </li>
                        <li><strong>Результат:</strong> Провалил обе проверки. Этот поставщик <span class="font-bold text-error">Нестабильный</span>.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
