# Generated by Django 5.2 on 2025-08-04 10:49

from django.db import migrations

FAIL_REASONS_DATA = [
    {
        "code": "FILE_FORMAT_UNRECOGNIZED",
        "name": "Нераспознанный формат файла",
        "description": "Система не смогла определить формат файла (например, .xls, .xlsx, .csv). Убедитесь, что файл имеет стандартное расширение.",
    },
    {
        "code": "FILE_READ_ERROR",
        "name": "Ошибка чтения файла",
        "description": "Файл поврежден или его не удалось прочитать. Попробуйте открыть файл на компьютере и пересохранить его.",
    },
    {
        "code": "COLUMN_MISSING_VALUES",
        "name": "Слишком много пропусков",
        "description": "В ключевых колонках (артикул, бренд, цена) отсутствует более 50% значений. Проверьте, что данные в файле заполнены корректно.",
    },
    {
        "code": "COLUMN_UNEXPECTED_NUMERIC",
        "name": "Неожиданные числа в тексте",
        "description": "В текстовой колонке (например, 'бренд') найдено более 30% числовых значений. Проверьте правильность выбора колонок.",
    },
    {
        "code": "BRAND_CROSS_MISMATCH",
        "name": "Бренды не найдены",
        "description": "Данные в файле не соответствуют внутреннему справочнику брендов. Возможно, требуется обновление справочника.",
    },
    {
        "code": "SKU_FORMAT_INVALID",
        "name": "Неверный формат артикулов",
        "description": "Менее 70% артикулов в файле соответствуют стандартному формату (буквы, цифры, дефисы). Проверьте колонку с артикулами.",
    },
    {
        "code": "PRICE_TOO_LOW",
        "name": "Слишком низкие цены",
        "description": "Более 30% цен в файле ниже минимального порога в 9 рублей. Проверьте правильность цен и разделителей в файле.",
    },
    {
        "code": "PRICE_DEVIATION_HIGH",
        "name": "Сильное отклонение цен",
        "description": "Цены в прайс-листе отклоняются от средних цен за последнюю неделю более чем на 30%.",
    },
    {
        "code": "VALUE_NEGATIVE",
        "name": "Отрицательные значения",
        "description": "В колонке с количеством или ценой найдены отрицательные числа. Эти значения должны быть положительными.",
    },
    {
        "code": "UNEXPECTED_PROCESSING_ERROR",
        "name": "Неожиданная ошибка обработки",
        "description": "Произошла общая ошибка во время обработки файла, не подпадающая под другие категории.",
    },
    {
        "code": "DIF_STEP1_WRITE_ERROR",
        "name": "Ошибка записи в dif.dif_step_1",
        "description": "Техническая ошибка при записи обработанных данных в промежуточную таблицу ClickHouse.",
    },
    {
        "code": "NAME_PRICE_WRITE_ERROR",
        "name": "Ошибка записи в dif.name_price",
        "description": "Техническая ошибка при записи обработанных данных в основную таблицу ClickHouse.",
    },
    {
        "code": "ROW_COUNT_THRESHOLD_EXCEEDED",
        "name": "Значительное изменение кол-ва строк",
        "description": "Количество строк в текущем файле значительно (более чем на 30%) отличается от предыдущего успешного файла.",
    },
]


def populate_fail_reasons(apps, schema_editor):
    FailReason = apps.get_model("pricelens", "FailReason")
    for reason_data in FAIL_REASONS_DATA:
        FailReason.objects.update_or_create(code=reason_data["code"], defaults=reason_data)


def backfill_investigations(apps, schema_editor):
    Investigation = apps.get_model("pricelens", "Investigation")
    FailReason = apps.get_model("pricelens", "FailReason")

    reasons_by_code = {reason.code: reason for reason in FailReason.objects.all()}

    for investigation in Investigation.objects.iterator():
        reason_obj = reasons_by_code.get(investigation.error_text)
        if reason_obj:
            investigation.fail_reason = reason_obj
            investigation.save(update_fields=["fail_reason"])


class Migration(migrations.Migration):
    dependencies = [
        ("pricelens", "0002_failreason_investigation_fail_reason"),
    ]

    operations = [
        migrations.RunPython(populate_fail_reasons, migrations.RunPython.noop),
        migrations.RunPython(backfill_investigations, migrations.RunPython.noop),
    ]