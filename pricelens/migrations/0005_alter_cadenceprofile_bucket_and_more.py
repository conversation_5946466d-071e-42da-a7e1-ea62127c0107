# Generated by Django 5.2 on 2025-08-12 06:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pricelens', '0004_remove_investigation_error_id_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cadenceprofile',
            name='bucket',
            field=models.CharField(choices=[('consistent', 'Стабильные'), ('inconsistent', 'Нестабильные'), ('dead', 'Мертвые'), ('new', 'Новые')], max_length=16),
        ),
        migrations.AlterField(
            model_name='cadenceprofile',
            name='median_gap_days',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cadenceprofile',
            name='sd_gap',
            field=models.FloatField(blank=True, null=True),
        ),
    ]
