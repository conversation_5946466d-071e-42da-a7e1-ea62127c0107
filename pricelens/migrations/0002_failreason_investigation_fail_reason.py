# Generated by Django 5.2 on 2025-08-04 10:49

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pricelens', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FailReason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(help_text="The internal enum value, e.g., 'FILE_READ_ERROR'", max_length=100, unique=True)),
                ('name', models.CharField(help_text="User-friendly name in Russian, e.g., 'Ошибка чтения файла'", max_length=255)),
                ('description', models.TextField(help_text='Detailed explanation for the user in Russian.')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='investigation',
            name='fail_reason',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='pricelens.failreason'),
        ),
    ]
