# Generated by Django 5.2 on 2025-08-04 06:04

import uuid

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("common", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CadenceProfile",
            fields=[
                ("created_at", models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "supplier",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        serialize=False,
                        to="common.supplier",
                    ),
                ),
                ("median_gap_days", models.PositiveIntegerField()),
                ("sd_gap", models.FloatField()),
                ("days_since_last", models.PositiveIntegerField()),
                ("last_success_date", models.DateField()),
                (
                    "bucket",
                    models.CharField(
                        choices=[("consistent", "Стабильные"), ("inconsistent", "Нестабильные"), ("dead", "Мертвые")],
                        max_length=16,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="CadenceDaily",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("date", models.DateField(db_index=True)),
                ("had_file", models.BooleanField(default=False)),
                ("attempts", models.PositiveIntegerField(default=0)),
                ("errors", models.PositiveIntegerField(default=0)),
                ("last_stage", models.CharField(blank=True, default="", max_length=32)),
                ("supplier", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="common.supplier")),
            ],
            options={
                "unique_together": {("date", "supplier")},
            },
        ),
        migrations.CreateModel(
            name="Investigation",
            fields=[
                ("created_at", models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("id", models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ("event_dt", models.DateTimeField(db_index=True)),
                ("error_id", models.PositiveIntegerField(db_index=True)),
                ("error_text", models.CharField(max_length=128)),
                ("stage", models.CharField(max_length=32)),
                ("file_path", models.TextField(blank=True, default="")),
                (
                    "status",
                    models.IntegerField(
                        choices=[(0, "Открыто"), (1, "Решено"), (2, "Не решено")], db_index=True, default=0
                    ),
                ),
                ("note", models.TextField(blank=True, default="")),
                ("investigated_at", models.DateTimeField(blank=True, null=True)),
                (
                    "investigator",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL
                    ),
                ),
                ("supplier", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="common.supplier")),
            ],
            options={
                "indexes": [
                    models.Index(fields=["status", "event_dt"], name="pricelens_i_status_554ed2_idx"),
                    models.Index(fields=["supplier", "event_dt"], name="pricelens_i_supplie_c8ef93_idx"),
                ],
            },
        ),
    ]
