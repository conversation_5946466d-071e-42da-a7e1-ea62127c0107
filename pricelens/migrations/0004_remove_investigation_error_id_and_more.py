# Generated by Django 5.2 on 2025-08-04 11:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pricelens', '0003_backfill_fail_reasons'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='investigation',
            name='error_id',
        ),
        migrations.RemoveField(
            model_name='investigation',
            name='error_text',
        ),
        migrations.AlterField(
            model_name='failreason',
            name='code',
            field=models.CharField(help_text="Внутренний код ошибки, напр. 'FILE_READ_ERROR'", max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='failreason',
            name='description',
            field=models.TextField(help_text='Подробное описание ошибки'),
        ),
        migrations.AlterField(
            model_name='failreason',
            name='name',
            field=models.Char<PERSON>ield(help_text="Удобочитаемое название ошибки, напр. 'Ошибка чтения файла'", max_length=255),
        ),
    ]
