# Pricelens: Техническая документация

> **Версия:** 1.0 (Завер<PERSON><PERSON>нный MVP)
> **Последнее обновление:** 6 августа 2025 г.

Этот документ описывает техническую реализацию и архитектуру модуля `pricelens`.

## 1. Назначение модуля

`Pricelens` — это система мониторинга контура загрузки прайс-листов. Ее задача — отслеживать, агрегировать и предоставлять интерфейс для расследования ошибок, возникающих на различных этапах пайплайна, а также анализировать ритмичность поставок от поставщиков.

## 2. Архитектура и поток данных

```mermaid
flowchart TD
    subgraph Suppliers
        S1["Supplier emails / FTP uploads"]
    end

    S1 -->|file| Robot["mail/ftp robot<br>+ consolidate_api"]
    Robot -->|success .csv| DailyStore["/root/daily_consolidation/…/consolidate.csv/"]
    Robot -->|HTTP POST| API["/api/v1/pricelens/log_event/"]
    Robot -->|raw data| CHLoad["Airflow DAG 23:50 MSK"]
    CHLoad -->|insert| CH["ClickHouse dif_step_1"]

    subgraph "admin2 (Django + Celery)"
        API -->|вызов| Utils["utils.log_investigation_event()"]
        Utils -->|запись| PG[(Postgres: pricelens_*)]
        
        subgraph "Pricelens UI"
            direction TB
            Views["Django Views"]
            Templates["Шаблоны (DaisyUI)"]
        end

        CeleryTasks["Celery Beat (Scheduled Tasks)"]

        PG -->|чтение| Views
        Views --> Templates
        CH -->|чтение| CeleryTasks
        CeleryTasks -->|запись| PG
    end

    subgraph Пользователь
        U["Инженер/Оператор"]
    end

    U -->|просмотр| Templates
```

## 3. Модели данных

Основа системы — несколько моделей Django, которые хранят информацию о событиях и состоянии поставщиков. Модели разделены между приложениями `common` и `pricelens` для лучшей организации.

### 3.1 `common.models.Supplier`

Центральная модель, представляющая собой поставщика. Является единым источником правды для всех данных, связанных с поставщиками.

- **Назначение:** Определяет уникального поставщика в системе.
- **Ключевые поля:**
    - `supid` (`PositiveIntegerField`, `primary_key`): Уникальный ID поставщика, соответствующий `dif_id` из ClickHouse.
    - `name` (`CharField`): Имя поставщика.

### 3.2 `pricelens.models.FailReason`

Новая модель для хранения подробной информации о причинах сбоев.

- **Назначение:** Предоставляет расшифровку кодов ошибок, делая их понятными для пользователей.
- **Ключевые поля:**
    - `code` (`CharField`, `unique`): Короткий, уникальный код ошибки (например, `FILE_READ_ERROR`).
    - `name` (`CharField`): Человекочитаемое название ошибки на русском языке.
    - `description` (`TextField`): Подробное описание проблемы и возможные шаги для ее решения.
- **Источник кодов:** Полный список кодов ошибок можно найти в сервисе `consolidate_api` в файле `config\config.py`, класс `FailReasons`.

### 3.3 `pricelens.models.Investigation`

Основная модель, представляющая собой единичное событие ошибки, требующее расследования.

- **Назначение:** Хранит все данные о конкретной ошибке: когда произошла, у какого поставщика, и на каком этапе.
- **Ключевые поля:**
    - `id` (`UUIDField`): Уникальный, непредсказуемый идентификатор.
    - `supplier` (`ForeignKey` к `common.Supplier`): Четкая связь с поставщиком, у которого произошла ошибка.
    - `fail_reason` (`ForeignKey` к `pricelens.FailReason`): Ссылка на объект с подробным описанием ошибки.
    - `status` (`IntegerChoices`): Статус расследования (`Открыто`, `Решено`, `Не решено`).
    - `investigator` (`ForeignKey` к `User`): Ссылка на пользователя, который проводит расследование.

### 3.4 `pricelens.models.CadenceProfile`

Агрегированная информация о "здоровье" и ритмичности каждого поставщика.

- **Назначение:** Хранит аналитические метрики, рассчитанные на основе данных из ClickHouse, для классификации поставщиков и выявления аномалий в их ритме поставок.
- **Ключевые поля:**
    - `supplier` (`OneToOneField` к `common.Supplier`): Связь с основной моделью поставщика.
    - `bucket` (`TextChoices`): Категория поставщика, рассчитанная по гибридной логике:
        - **`dead`**: Поставок не было 28 или более дней.
        - **`consistent` (Стабильные)**: Поставщик считается стабильным, если выполняется **хотя бы одно** из условий:
            1.  Общая нестабильность (стандартное отклонение `sd_gap`) не превышает типичный интервал (`med_gap`).
            2.  Процент "плохих" промежутков (выбросов, которые более чем вдвое превышают `med_gap`) составляет **менее 20%** от общего числа поставок.
        - **`inconsistent` (Нестабильные)**: Все остальные поставщики, которые не являются "мертвыми", но провалили обе проверки на стабильность.
- **Обоснование логики:** Гибридный подход был выбран, чтобы система была устойчива к единичным, большим сбоям в поставках (которые могли произойти по нашей вине), но при этом оставалась чувствительной к поставщикам с действительно частыми и непредсказуемыми сбоями.

### 3.5 `pricelens.models.CadenceDaily`

Ежедневная сводка по каждому поставщику.

- **Назначение:** Детализированная статистика за каждый день: были ли файлы, сколько было попыток загрузки, сколько ошибок.
- **Ключевые поля:**
    - `date` (`DateField`)
    - `supplier` (`ForeignKey` к `common.Supplier`)
    - `unique_together`: (`date`, `supplier`)

## 4. Пользовательский интерфейс

Реализован полнофункциональный UI с использованием Django Templates и **DaisyUI**.

- **Dashboard (`/pricelens/`):** Главная панель с ключевыми метриками.
- **Queue (`/pricelens/queue/`):** Таблица со списком всех расследований с пагинацией и фильтрацией по статусу.
- **Cadence (`/pricelens/cadence/`):** Страница для просмотра ритмичности поставщиков с фильтрацией по категориям.
- **Investigate (`/pricelens/investigate/<id>/`):** Детальная страница для одного расследования с формой для добавления заметок и смены статуса.

## 5. API и Сериализаторы

Для приема событий из внешних сервисов реализован REST API endpoint, который использует специальный сериализатор для валидации данных.

### 5.1 `POST /api/v1/pricelens/log_event/`

- **Назначение:** Основная точка входа для регистрации событий ошибок из `load_mail`, `load_ftp` и `consolidate`.
- **Аутентификация:** `TokenAuthentication`. Запросы должны содержать заголовок `Authorization: Token <your_token>`.
- **Тело запроса (JSON):

```json
{
    "event_dt": "2025-08-04T12:30:00Z",
    "supid": 1234,
    "reason": "FILE_READ_ERROR",
    "stage": "load_mail",
    "file_path": "/path/to/failed/file.xlsx"
}
```

### 5.2 `pricelens.serializers.LogEventSerializer`

- **Класс:** `serializers.Serializer` (не `ModelSerializer`).
- **Обоснование выбора:**
    1.  **Разделение контрактов:** API принимает данные в одном формате (DTO - Data Transfer Object), а модель `Investigation` хранит их в другом. Сериализатор выступает в роли четкого контракта для API, не привязанного к структуре БД.
    2.  **Трансформация данных:** Входящее поле `reason` (строковый код ошибки) используется для поиска или создания соответствующего объекта `FailReason` в базе данных. `Serializer` позволяет гибко управлять этой логикой в представлении (`view`) или утилите (`utils`).
    3.  **Поля, генерируемые сервером:** Такие поля, как `status`, `created_at` и `id`, устанавливаются сервером и не должны приниматься от клиента. `Serializer` упрощает эту логику.

## 6. Автоматизированные фоновые задачи (Celery)

Для полной автоматизации сбора и анализа данных в `pricelens` используются асинхронные задачи, выполняемые по расписанию с помощью Celery Beat.

- **Расположение:** `pricelens/tasks.py`
- **Расписание:** `config/third_party_config/celery.py`

### 6.1 `backfill_suppliers_task`

- **Назначение:** Синхронизирует список поставщиков между ClickHouse и основной базой данных Postgres.
- **Расписание:** Еженедельно, по понедельникам в 05:00 МСК.

### 6.2 `refresh_cadence_profiles_task`

- **Назначение:** Основная аналитическая задача. Выполняет ресурсоемкий запрос к ClickHouse для анализа данных за последние 180 дней, рассчитывает метрики ритмичности (`median_gap`, `sd_gap`) и обновляет `CadenceProfile` для каждого поставщика, присваивая ему соответствующий бакет (`consistent`, `inconsistent`, `dead`).
- **Расписание:** Ежедневно, в 06:30 МСК.

### 6.3 `backfill_investigations_task`

- **Назначение:** Задача-страховка. Ежедневно проверяет ClickHouse на наличие событий ошибок за прошедший день, которые могли быть пропущены API (например, из-за сбоя сети). Гарантирует полноту данных в очереди расследований. Задача идемпотентна и не создает дубликатов.
- **Расписание:** Ежедневно, в 06:35 МСК.