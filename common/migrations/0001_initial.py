# Generated by Django 5.2 on 2025-08-04 06:04

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('created_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('supid', models.PositiveIntegerField(help_text="The supplier's unique ID (dif_id from ClickHouse)", primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('is_partner', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
